
allprojects {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    configurations.all {
        resolutionStrategy {
            def projectKotlinVersion = "1.9.10"

            // Added for msal_auth
            force "org.jetbrains.kotlin:kotlin-gradle-plugin:$projectKotlinVersion"
            force "org.jetbrains.kotlin:kotlin-stdlib:$projectKotlinVersion"

            // Add these lines to fix the annotation-jvm compatibility issue
            // For vis-am's system
            force "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$projectKotlinVersion"
            force "org.jetbrains.kotlin:kotlin-reflect:$projectKotlinVersion"
            force "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$projectKotlinVersion"
            force "org.jetbrains.kotlin:kotlin-stdlib-common:$projectKotlinVersion"
            force "org.jetbrains.kotlin:kotlin-annotation-jvm:$projectKotlinVersion" 
            force "androidx.annotation:annotation:1.5.0" 
        }
    }
}


rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
