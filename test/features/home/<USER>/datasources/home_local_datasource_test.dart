import 'package:flutter_test/flutter_test.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/datasources/home_local_datasource.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';

void main() {
  group('HomeLocalDataSource Calendar Tests', () {
    late HomeLocalDataSourceImpl dataSource;
    late RealmDatabase realmDatabase;

    setUp(() {
      realmDatabase = RealmDatabase.instance;
      dataSource = HomeLocalDataSourceImpl(realmDatabase: realmDatabase);
    });

    test('should save and retrieve calendar info', () async {
      // Arrange
      final calendarInfo = CalendarInfo(
        timestamp: DateTime(2024, 1, 15),
        dollarSymbol: true,
        publicHoliday: false,
        budgetAmount: 100.50,
      );

      final data = Data(calendarInfo: [calendarInfo]);
      final calendarResponse = CalendarResponseEntity(data: data);

      // Act - Save
      await dataSource.saveCalendarInfo(calendarResponse);

      // Act - Retrieve
      final result = await dataSource.getCalendarInfo();

      // Assert
      expect(result, isNotNull);
      expect(result!.data, isNotNull);
      expect(result.data!.calendarInfo, isNotNull);
      expect(result.data!.calendarInfo!.length, equals(1));

      final retrievedInfo = result.data!.calendarInfo!.first;
      expect(retrievedInfo.timestamp, equals(DateTime(2024, 1, 15)));
      expect(retrievedInfo.dollarSymbol, equals(true));
      expect(retrievedInfo.publicHoliday, equals(false));
      expect(retrievedInfo.budgetAmount, equals(100.50));
    });

    test('should return null when no calendar info is stored', () async {
      // Act
      final result = await dataSource.getCalendarInfo();

      // Assert
      expect(result, isNull);
    });

    test('should replace existing calendar info when saving new data',
        () async {
      // Arrange - Save first set of data
      final firstCalendarInfo = CalendarInfo(
        timestamp: DateTime(2024, 1, 15),
        dollarSymbol: true,
        publicHoliday: false,
        budgetAmount: 100.0,
      );
      final firstData = Data(calendarInfo: [firstCalendarInfo]);
      final firstResponse = CalendarResponseEntity(data: firstData);

      await dataSource.saveCalendarInfo(firstResponse);

      // Arrange - Save second set of data
      final secondCalendarInfo = CalendarInfo(
        timestamp: DateTime(2024, 1, 16),
        dollarSymbol: false,
        publicHoliday: true,
        budgetAmount: 200.0,
      );
      final secondData = Data(calendarInfo: [secondCalendarInfo]);
      final secondResponse = CalendarResponseEntity(data: secondData);

      // Act
      await dataSource.saveCalendarInfo(secondResponse);
      final result = await dataSource.getCalendarInfo();

      // Assert - Should only have the second data
      expect(result, isNotNull);
      expect(result!.data!.calendarInfo!.length, equals(1));

      final retrievedInfo = result.data!.calendarInfo!.first;
      expect(retrievedInfo.timestamp, equals(DateTime(2024, 1, 16)));
      expect(retrievedInfo.dollarSymbol, equals(false));
      expect(retrievedInfo.publicHoliday, equals(true));
      expect(retrievedInfo.budgetAmount, equals(200.0));
    });
  });
}
