import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/storage/storage_service.dart';
import 'package:storetrack_app/features/auth/data/models/login_response.dart';

void main() {
  late DataManager dataManager;
  late StorageService storageService;

  setUp(() async {
    // Initialize SharedPreferences for testing
    SharedPreferences.setMockInitialValues({});
    final sharedPrefs = await SharedPreferences.getInstance();

    // Create the storage service and data manager
    storageService = StorageServiceImpl(sharedPrefs);
    dataManager = DataManagerImpl(storageService);
  });

  test('saveLoginResponse and getLoginResponse should work correctly',
      () async {
    // Create a test login response
    final loginResponse = LoginResponse(
      data: Data(
        userId: 123,
        token: 'test_token',
        isPriceCheckerUser: true,
        isAdminUniversal: false,
        dayCheckIn: true,
        pos: false,
        openTasks: true,
        vacancies: false,
        premAutoSchedule: true,
        premAvailability: false,
        premAutoSchedule4Weeks: true,
        watermarkImages: false,
      ),
    );

    // Save the login response
    await dataManager.saveLoginResponse(loginResponse);

    // Retrieve the login response
    final retrievedResponse = await dataManager.getLoginResponse();

    // Verify that the retrieved response matches the original
    expect(retrievedResponse?.data?.userId, equals(loginResponse.data?.userId));
    expect(retrievedResponse?.data?.token, equals(loginResponse.data?.token));
    expect(retrievedResponse?.data?.isPriceCheckerUser,
        equals(loginResponse.data?.isPriceCheckerUser));
    expect(retrievedResponse?.data?.isAdminUniversal,
        equals(loginResponse.data?.isAdminUniversal));
    expect(retrievedResponse?.data?.dayCheckIn,
        equals(loginResponse.data?.dayCheckIn));
    expect(retrievedResponse?.data?.pos, equals(loginResponse.data?.pos));
    expect(retrievedResponse?.data?.openTasks,
        equals(loginResponse.data?.openTasks));
    expect(retrievedResponse?.data?.vacancies,
        equals(loginResponse.data?.vacancies));
    expect(retrievedResponse?.data?.premAutoSchedule,
        equals(loginResponse.data?.premAutoSchedule));
    expect(retrievedResponse?.data?.premAvailability,
        equals(loginResponse.data?.premAvailability));
    expect(retrievedResponse?.data?.premAutoSchedule4Weeks,
        equals(loginResponse.data?.premAutoSchedule4Weeks));
    expect(retrievedResponse?.data?.watermarkImages,
        equals(loginResponse.data?.watermarkImages));
  });
}
