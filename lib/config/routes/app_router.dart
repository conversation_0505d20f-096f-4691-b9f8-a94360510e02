import 'package:auto_route/auto_route.dart';

// Force regeneration of app_router.gr.dart
import 'app_router.gr.dart';

@AutoRouterConfig(replaceInRouteName: 'Screen|Page,Route')
class AppRouter extends RootStackRouter {
  @override
  RouteType get defaultRouteType => const RouteType.cupertino();

  @override
  List<AutoRoute> get routes => [
        AutoRoute(
          page: ResetPasswordRoute.page,
        ),
        AutoRoute(
          page: SplashRoute.page,
          initial: true,
        ),
        AutoRoute(
          page: LoginRoute.page,
        ),
        AutoRoute(
          page: HomeRoute.page,
          children: [
            AutoRoute(
              page: DashboardHolderRoute.page,
              initial: true,
              children: [
                AutoRoute(page: UnscheduledRoute.page),
                <PERSON>Route(page: ScheduleRoute.page),
                AutoRoute(page: NotificationsRoute.page),
                AutoRoute(page: TodayRoute.page),
                AutoRoute(page: JourneyMapRoute.page),
                AutoRoute(
                  page: TaskDetailsRoute.page,
                ),
                AutoRoute(
                  page: DashboardRoute.page,
                  initial: true,
                ),
              ],
            ),
            <PERSON>Route(page: AssistantRoute.page),
            <PERSON>Route(page: ProfileRoute.page),
            AutoRoute(page: MoreRoute.page),
          ],
        ),
        // Removed DashboardRoute children to avoid duplicate UnscheduledRoute
        // UnscheduledRoute will only be accessible as a child of HomeRoute.
        // If you need nested navigation in Dashboard, handle it via HomeRoute's children.
        AutoRoute(
          page: NotificationsRoute.page,
        ),
        AutoRoute(
          page: WebBrowserRoute.page,
        ),
        AutoRoute(
          page: TaskDetailsRoute.page,
        ),
        // Removed top-level UnscheduledRoute. Now only accessible under HomeRoute (with bottom nav bar).
      ];
}
