import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'app_colors.dart';
import 'app_fonts.dart';
import 'app_typography.dart';

abstract class AppTheme {
  static ThemeData get light {
    return ThemeData(
      appBarTheme: const AppBarTheme(
        elevation: 4,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        color: Colors.white,
        titleTextStyle: AppTypography.montserrat,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      ),
      fontFamily: AppFonts.montserrat,
      scaffoldBackgroundColor: Colors.white,
      primaryColor: AppColors.primaryBlue,
      splashColor: Colors.transparent,
      colorScheme: ColorScheme.fromSeed(seedColor: AppColors.primaryBlue),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: AppColors.primaryBlue,
      ),
      textTheme: const TextTheme(
        headlineLarge: AppTypography.montserrat,
        headlineMedium: AppTypography.montserrat,
        titleLarge: AppTypography.montserrat,
        bodyLarge: AppTypography.montserrat,
        bodyMedium: AppTypography.montserrat,
      ),
    );
  }
}
