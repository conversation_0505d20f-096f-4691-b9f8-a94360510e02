import 'package:flutter/material.dart';

import 'app_colors.dart';
import 'app_fonts.dart';

class AppTypography {
  static const montserrat = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontSize: 14,
  );

  static const TextStyle montserratSemiBold = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w600,
    fontSize: 32,
    color: AppColors.black,
  );

  static const TextStyle montserratBold = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w700,
    fontSize: 32,
    color: AppColors.black,
  );

  static const TextStyle montserratMedium = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w500,
    fontSize: 32,
    color: AppColors.black,
  );

  static const TextStyle montserratRegular = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w400,
    fontSize: 32,
    color: AppColors.black,
  );

  static const TextStyle montserratLight = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w300,
    fontSize: 14,
    color: AppColors.black,
  );

  static const TextStyle montserratTitleExtraSmall = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w500,
    fontSize: 14,
    color: AppColors.black,
  );

  static const TextStyle montserratTableSmall = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w400,
    fontSize: 12,
    color: AppColors.black,
  );

  static const TextStyle montserratTitleSmall = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w500,
    fontSize: 16,
    color: AppColors.black,
  );

  static const TextStyle montserratParagraphSmall = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w400,
    fontSize: 14,
    color: AppColors.black,
  );

  static const TextStyle montserratParagraphXsmall = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w400,
    fontSize: 12,
    color: AppColors.black,
  );

  static const TextStyle montserratTitleXxsmall = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w500,
    fontSize: 12,
    color: AppColors.black,
  );

  static const TextStyle montserratHeadingMedium = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w600,
    fontSize: 18,
    color: AppColors.black,
  );

  static const TextStyle montserratFormsfield = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w500,
    fontSize: 14,
    color: AppColors.black,
  );

  static const TextStyle montserratTitleLarge = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w400,
    fontSize: 32,
    color: AppColors.black,
  );

  static const TextStyle montserratBottomNavigation = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w600,
    fontSize: 10,
    color: AppColors.black,
  );
  static const TextStyle montserratNavigationPrimaryMedium = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w600,
    fontSize: 14,
    color: AppColors.black,
  );
  static const TextStyle montserratMetricsAxisRegular = TextStyle(
    fontFamily: AppFonts.montserrat,
    fontWeight: FontWeight.w600,
    fontSize: 12,
    color: AppColors.black,
  );
}
