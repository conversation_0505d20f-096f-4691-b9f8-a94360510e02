class AppAssets {
  static const String banner = 'assets/images/storetrack_banner.png';
  static const String poweredBy = 'assets/images/powered_by_crossmark.png';
  static const String homeDashboard = 'assets/images/bottom_dashboard.png';
  static const String homeAssistant = 'assets/images/bottom_assistant.png';
  static const String homeMore = 'assets/images/bottom_more.png';
  static const String homeProfile = 'assets/images/bottom_profile.png';
  static const String autoScheduleIcon = 'assets/images/auto_schedule_icon.png';
  static const String scheduledIcon = 'assets/images/scheduled_icon.png';
  static const String todayIcon = 'assets/images/today_icon.png';
  static const String unscheduledIcon = 'assets/images/unscheduled_icon.png';
  static const String alertMessage = 'assets/images/alert_message.png';
  static const String alertLightening = 'assets/images/alert_lightning.png';
  static const String alertIcon = 'assets/images/alert_icon.png';
  static const String documentsIcon = 'assets/images/documents_icon.png';
  static const String formsIcon = 'assets/images/forms_icon.png';
  static const String posIcon = 'assets/images/pos_icon.png';
  static const String supportDocument = 'assets/images/support_doc.png';
  static const String supportMedia = 'assets/images/support_media.png';
  static const String bgImage = 'assets/images/background_image.png';
  static const String dashboardNotification =
      'assets/images/notification_dashboard.png';
  static const String dashboardPos = 'assets/images/dashboard_pos.png';
  static const String dashboardOpenTasks =
      'assets/images/dashboard_open_tasks.png';
  static const String dashboardHistory = 'assets/images/dashboard_history.png';
  static const String dashboardToday = 'assets/images/dashboard_today.png';
  static const String notificationLighteningBlack =
      'assets/images/notification_lightening_black.png';
  static const String notificationDollar =
      'assets/images/notification_dollar.png';
  static const String notificationLocation =
      'assets/images/notification_location.png';
  static const String imgBg = 'assets/images/img_bg.png';
  static const calendarTime = 'assets/images/calendar_time.png';
  static const emulateGridImage = 'assets/images/emulate_grid_image.png';
  static const appbarMap = 'assets/images/appbarmap.png';
  static const appbarCalendarEdit = 'assets/images/appbarcalender-edit.png';
  static const appbarCalendar = 'assets/images/appbarcalendar.png';
  static const appbarRecentTime = 'assets/images/appbarrecenttime.png';
  static const appbarList = 'assets/images/appbarlist.png';
  static const dayTimer = 'assets/images/daytimer.png';
  static const taskReport = 'assets/images/task_report.png';
  static const taskMore = 'assets/images/task_more.png';
  static const taskAssistant = 'assets/images/task_assistant.png';
  static const taskForm = 'assets/images/task_form.png';
  static const taskStoryHistory = 'assets/images/task_store_history.png';
  static const taskStore = 'assets/images/task_store.png';
  static const taskComplete = 'assets/images/task_completed.png';
}
