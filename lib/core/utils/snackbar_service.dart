import 'package:flutter/material.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import '../../config/themes/app_colors.dart';

class SnackBarService {
  // Private constructor to prevent direct instantiation
  SnackBarService._();

  static void show({
    required BuildContext context,
    required String message,
    Color? backgroundColor,
    Color? textColor,
    SnackBarType type = SnackBarType.info,
    double? positionFromBottom,
  }) {
    // Remove any existing SnackBars
    ScaffoldMessenger.of(context).removeCurrentSnackBar();

    // Determine background and text colors based on type
    final colors = _getColorByType(type, backgroundColor, textColor);
    final bgColor = colors['backgroundColor']!;
    final txtColor = colors['textColor']!;

    final textTheme = Theme.of(context).textTheme;

    // Determine the vertical margin
    final double verticalMargin = positionFromBottom ?? 8.0;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        elevation: 0,
        content: Text(
          message,
          // Assuming bodyRegular exists via your extension
          style: textTheme.montserratBold.copyWith(
            fontWeight: FontWeight.w400,
            fontSize: 14,
            color: txtColor,
          ),
          overflow: TextOverflow.ellipsis,
          maxLines: 6,
          textAlign: TextAlign.start,
        ),
        backgroundColor: bgColor,
        behavior: SnackBarBehavior.floating,
        // Use the calculated vertical margin
        margin: EdgeInsets.symmetric(
          horizontal: 16,
          vertical: verticalMargin,
        ),
        duration: const Duration(seconds: 3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
          side: const BorderSide(
            color: Colors.transparent,
          ),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 18,
        ),
      ),
    );
  }

  static Map<String, Color> _getColorByType(
      SnackBarType type, Color? customBackgroundColor, Color? customTextColor) {
    Color backgroundColor;
    Color textColor = customTextColor ?? Colors.white;

    if (customBackgroundColor != null) {
      backgroundColor = customBackgroundColor;
    } else {
      switch (type) {
        case SnackBarType.success:
          backgroundColor = Colors.green;
          break;
        case SnackBarType.error:
          backgroundColor = AppColors.loginRed;
          textColor = Colors.white;
          break;
        case SnackBarType.warning:
          backgroundColor = AppColors.loginRed;
          textColor = Colors.white;
          break;
        case SnackBarType.info:
          backgroundColor = AppColors.green15;
          textColor = AppColors.green50;
          break;
        default:
          backgroundColor = AppColors.primaryBlue;
      }
    }

    return {
      'backgroundColor': backgroundColor,
      'textColor': textColor,
    };
  }

  // Convenience methods for specific types
  static void success({
    required BuildContext context,
    required String message,
    Color? backgroundColor,
    Color? textColor,
    double? positionFromBottom,
  }) {
    show(
      context: context,
      message: message,
      backgroundColor: backgroundColor,
      textColor: textColor,
      type: SnackBarType.success,
      positionFromBottom: positionFromBottom,
    );
  }

  static void error({
    required BuildContext context,
    required String message,
    Color? backgroundColor,
    Color? textColor,
    double? positionFromBottom,
  }) {
    show(
      context: context,
      message: message,
      backgroundColor: backgroundColor,
      textColor: textColor,
      type: SnackBarType.error,
      positionFromBottom: positionFromBottom,
    );
  }

  static void warning({
    required BuildContext context,
    required String message,
    Color? backgroundColor,
    Color? textColor,
    double? positionFromBottom,
  }) {
    show(
      context: context,
      message: message,
      backgroundColor: backgroundColor,
      textColor: textColor,
      type: SnackBarType.warning,
      positionFromBottom: positionFromBottom,
    );
  }

  static void info({
    required BuildContext context,
    required String message,
    Color? backgroundColor,
    Color? textColor,
    double? positionFromBottom,
  }) {
    show(
      context: context,
      message: message,
      backgroundColor: backgroundColor,
      textColor: textColor,
      type: SnackBarType.info,
      positionFromBottom: positionFromBottom,
    );
  }
}

// Enum for SnackBar types
enum SnackBarType { success, error, warning, info }
