import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';
import 'package:storetrack_app/core/utils/logger.dart';

String createJwt(String emailText) {
  final issueDate = DateTime.now();
  final expirationDate = issueDate.add(const Duration(hours: 24));

  final jwtPayload = {
    'iss': 'crossmark.com.au',
    'linkCss': '',
    'exp': expirationDate.millisecondsSinceEpoch ~/ 1000,
    'iat': issueDate.millisecondsSinceEpoch ~/ 1000,
    'context': {
      'weekStart': 0,
    },
    'user': {
      'userId': emailText,
    },
  };

  final jwt = JWT(jwtPayload);

  String jwtString = '';

  try {
    jwtString = jwt.sign(SecretKey('9cSdF@HJTOpeykW%'));
  } catch (e) {
    logger(e.toString());
  }
  return jwtString;
}
