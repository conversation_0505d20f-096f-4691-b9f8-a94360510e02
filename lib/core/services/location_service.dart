import 'package:location/location.dart';

/// Service for handling location-related functionality
abstract class LocationService {
  /// Checks if location services are enabled on the device
  Future<bool> isLocationServiceEnabled();

  /// Checks if the app has permission to access location
  Future<bool> hasLocationPermission();

  /// Requests location permission from the user
  Future<bool> requestLocationPermission();

  /// Checks if both location services are enabled and permission is granted
  Future<bool> isLocationAvailable();

  /// Gets the current position of the device
  Future<LocationData?> getCurrentPosition();
}

/// Implementation of [LocationService]
class LocationServiceImpl implements LocationService {
  final Location _location = Location();

  @override
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await _location.serviceEnabled();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> hasLocationPermission() async {
    try {
      final permission = await _location.hasPermission();
      return permission == PermissionStatus.granted;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> requestLocationPermission() async {
    try {
      final permission = await _location.requestPermission();
      return permission == PermissionStatus.granted;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> isLocationAvailable() async {
    final isEnabled = await isLocationServiceEnabled();
    if (!isEnabled) {
      final serviceRequested = await _location.requestService();
      if (!serviceRequested) {
        return false;
      }
    }

    final hasPermission = await hasLocationPermission();
    if (!hasPermission) {
      final permissionGranted = await requestLocationPermission();
      return permissionGranted;
    }

    return true;
  }

  @override
  Future<LocationData?> getCurrentPosition() async {
    try {
      final isAvailable = await isLocationAvailable();
      if (!isAvailable) {
        return null;
      }

      // Configure location settings
      await _location.changeSettings(
        accuracy: LocationAccuracy.high,
        interval: 1000,
        distanceFilter: 0,
      );

      return await _location.getLocation();
    } catch (e) {
      return null;
    }
  }
}
