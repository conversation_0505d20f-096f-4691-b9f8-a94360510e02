import 'package:flutter/material.dart';

import '../../config/themes/app_typography.dart';

extension ThemeExtensions on TextTheme {
  TextStyle get montserrat => AppTypography.montserrat; // Weight 400
  TextStyle get montserratBold => AppTypography.montserratBold; // Weight 700
  TextStyle get montserratSemiBold =>
      AppTypography.montserratSemiBold; // Weight 600
  TextStyle get montserratMedium =>
      AppTypography.montserratMedium; // Weight 500
  TextStyle get montserratRegular =>
      AppTypography.montserratRegular; // Weight 400
  TextStyle get montserratLight => AppTypography.montserratLight; // Weight 300
  TextStyle get montserratTitleSmall => AppTypography.montserratTitleSmall;
  TextStyle get montserratTitleExtraSmall =>
      AppTypography.montserratTitleExtraSmall;
  TextStyle get montserratTableSmall => AppTypography.montserratTableSmall;
  TextStyle get montserratParagraphSmall =>
      AppTypography.montserratParagraphSmall;
  TextStyle get montserratParagraphXsmall =>
      AppTypography.montserratParagraphXsmall;
  TextStyle get montserratTitleXxsmall => AppTypography.montserratTitleXxsmall;
  TextStyle get montserratheadingmedium =>
      AppTypography.montserratHeadingMedium;
  TextStyle get montserratFormsField => AppTypography.montserratFormsfield;
  TextStyle get montserratTitleLarge => AppTypography.montserratTitleLarge;
  TextStyle get montserratBottomNavigation =>
      AppTypography.montserratBottomNavigation;
  TextStyle get montserratNavigationPrimaryMedium =>
      AppTypography.montserratNavigationPrimaryMedium;
  TextStyle get montserratMetricsAxisRegular =>
      AppTypography.montserratMetricsAxisRegular;
}
