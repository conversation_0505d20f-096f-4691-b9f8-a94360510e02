import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:storetrack_app/core/utils/logger.dart';

/// Interface for storage service
abstract class StorageService {
  /// Writes a data to the shared preferences.
  Future<void> writeData(String key, dynamic value);

  /// Reads a data from the shared preferences.
  Future<T?> readData<T>(String key);

  /// Deletes a data from the shared preferences.
  Future<void> deleteData(String key);

  /// Clears all data from both secure storage and shared preferences.
  Future<void> clearAll();
}

/// Implementation of [StorageService] using
/// FlutterSecureStorage and SharedPreferences.
class StorageServiceImpl implements StorageService {
  /// Creates a [StorageServiceImpl] instance.
  ///
  /// The SecureStorage and SharedPrefs must not be null.
  StorageServiceImpl(this._sharedPrefs);

  /// The shared preferences used to store non-sensitive data.
  final SharedPreferences _sharedPrefs;

  @override
  Future<T?> readData<T>(String key) async {
    try {
      // Retrieve the value associated with the key from shared preferences.
      final value = _sharedPrefs.get(key);

      // Direct type match
      if (value is T) {
        return value;
      }
      // Handle Map<String, dynamic> case
      else if (value is String && T.toString() == 'Map<String, dynamic>') {
        try {
          final Map<String, dynamic> jsonMap = json.decode(value);
          return jsonMap as T;
        } catch (e) {
          logger('Error decoding JSON: $e');
          return null;
        }
      } else {
        return null;
      }
    } on Exception catch (e) {
      // Handle the error. For example, log it or rethrow it.
      logger('Error reading data: $e');
      return null;
    }
  }

  @override
  Future<void> clearAll() async {
    try {
      // Clear all data from shared preferences and secure storage.
      await _sharedPrefs.clear();
    } on Exception {
      // Handle the error. For example, log it or rethrow it.
    }
  }

  @override
  Future<void> deleteData(String key) async {
    try {
      // Remove the value associated with the key from shared preferences.
      await _sharedPrefs.remove(key);
    } on Exception {
      // Handle the error. For example, log it or rethrow it.
    }
  }

  @override
  Future<void> writeData(String key, dynamic value) async {
    try {
      // Store the value associated with the key in shared preferences.
      if (value is String) {
        await _sharedPrefs.setString(key, value);
      } else if (value is int) {
        await _sharedPrefs.setInt(key, value);
      } else if (value is double) {
        await _sharedPrefs.setDouble(key, value);
      } else if (value is bool) {
        await _sharedPrefs.setBool(key, value);
      } else if (value is List<String>) {
        await _sharedPrefs.setStringList(key, value);
      } else if (value is Map<String, dynamic>) {
        // Convert Map to JSON string and store it
        final jsonString = json.encode(value);
        await _sharedPrefs.setString(key, jsonString);
      } else {
        // Handle other unsupported value types
        logger('Unsupported type for SharedPreferences: ${value.runtimeType}');
      }
    } on Exception catch (e) {
      // Handle the error. For example, log it or rethrow it.
      logger('Error writing data: $e');
    }
  }
}
