import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/create_jwt.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/data/models/auth_request.dart';
import 'package:storetrack_app/features/auth/data/models/auth_response.dart';
import 'package:storetrack_app/features/auth/data/models/login_request.dart';
import 'package:storetrack_app/features/auth/domain/usecases/login_nz_use_case.dart';
import 'package:storetrack_app/features/auth/domain/usecases/login_use_case.dart';

import '../../../../../core/utils/logger.dart';
import '../../../data/models/login_response.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit(
    this._loginNZUseCase,
    this._loginUseCase,
  ) : super(AuthInitial());

  final LoginNZUseCase _loginNZUseCase;
  final LoginUseCase _loginUseCase;

  Future<void> loginNZ({
    required AuthRequest authRequest,
  }) async {
    emit(AuthLoading());
    var authResponse = await _loginNZUseCase.call(authRequest);
    logger(authResponse.data?.toJson().toString() ?? "haha5");
    if (authResponse.isSuccess) {
      logger("haha2");
      emit(AuthSuccess(authResponse.data!));
    } else {
      logger("haha3 ${authResponse.error}");
      emit(AuthError(authResponse.error!));
    }
  }

  Future<void> loginAzure() async {
    emit(AuthLoading());
    emit(AuthSuccess(AuthResponse()));
  }

  Future<void> login({
    required String email,
  }) async {
    emit(AuthLoading());
    var jwt = createJwt(email);
    var loginRequest = LoginRequest(
      azureJwt: jwt,
      deviceToken: "abcd",
      devicePlatform: "android",
      appversion: "1",
      deviceuid: "abcd",
      devicename: "abcd",
      devicemodel: "abcd",
      deviceversion: "abcd",
    );
    var loginResponse = await _loginUseCase.call(loginRequest);
    if (loginResponse.isSuccess) {
      sl<DataManager>().saveLoginResponse(loginResponse.data!);
      sl<DataManager>().saveAuthToken(jwt);
      sl<DataManager>().saveEmail(email);
      emit(LoginSuccess(loginResponse.data!));
    } else {
      emit(AuthError(loginResponse.error!));
    }
  }
}
