part of 'auth_cubit.dart';

sealed class AuthState {
  const AuthState();
}

final class AuthInitial extends AuthState {}

final class AuthLoading extends AuthState {}

final class AuthSuccess extends AuthState {
  final AuthResponse authResponse;

  const AuthSuccess(this.authResponse);
}

final class LoginSuccess extends AuthState {
  final LoginResponse loginResponse;

  const LoginSuccess(this.loginResponse);
}

final class AuthAuthenticated extends AuthState {
  final AuthResponse authResponse;

  const AuthAuthenticated({
    required this.authResponse,
  });
}

final class AuthError extends AuthState {
  final String message;

  const AuthError(this.message);
}
