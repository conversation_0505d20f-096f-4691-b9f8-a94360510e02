part of 'reset_password_cubit.dart';

abstract class ResetPasswordState extends Equatable {
  @override
  List<Object?> get props => [];
}

class ResetPasswordInitial extends ResetPasswordState {}

class ResetPasswordLoading extends ResetPasswordState {}

class ResetPasswordSuccess extends ResetPasswordState {
  final AuthResponse data;
  ResetPasswordSuccess(this.data);
  @override
  List<Object?> get props => [data];
}

class ResetPasswordError extends ResetPasswordState {
  final String message;
  ResetPasswordError(this.message);
  @override
  List<Object?> get props => [message];
}
