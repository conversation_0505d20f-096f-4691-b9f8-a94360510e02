import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/domain/usecases/reset_pw_nz_use_case.dart';
import 'package:storetrack_app/features/auth/data/models/auth_response.dart';
import 'package:storetrack_app/core/utils/logger.dart';

import '../../../../../core/storage/data_manager.dart';

part 'reset_password_state.dart';

class ResetPasswordCubit extends Cubit<ResetPasswordState> {
  final ResetPwNzUseCase _resetPwNzUseCase;

  ResetPasswordCubit(this._resetPwNzUseCase) : super(ResetPasswordInitial());

  Future<void> resetPwNz({required String email}) async {
    emit(ResetPasswordLoading());
    var authResponse = await _resetPwNzUseCase.call(email);
    logger(authResponse.data?.toJson().toString() ?? "resetPwNz: no data");
    if (authResponse.isSuccess) {
      sl<DataManager>().saveEmail(authResponse.data?.data?.personalEmail ?? "");
      emit(ResetPasswordSuccess(authResponse.data!));
    } else {
      logger("resetPwNz error: [31m${authResponse.error}[0m");
      emit(ResetPasswordError(authResponse.error!));
    }
  }
}
