import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/features/auth/presentation/blocs/reset_password/reset_password_cubit.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../../shared/widgets/retry_widget.dart';
import '../../../../shared/widgets/custom_app_bar.dart';
import '../../../auth/presentation/widgets/app_button.dart';
import '../../../auth/presentation/widgets/app_text_field.dart';

@RoutePage()
class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({super.key, required this.email});

  final String email;

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> {
  final TextEditingController _usernameController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _loading = false;

  @override
  void dispose() {
    _usernameController.dispose();
    super.dispose();
  }

  void _handleReset() {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _loading = true);
    _sendEmail();
  }

  String? _validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return 'Username or email is required';
    }
    // Simple email regex
    final emailRegex = RegExp(r'^[^@\s]+@[^@\s]+\.[^@\s]+$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  _sendEmail() {
    final email = _usernameController.text.trim().toLowerCase();
    final isMsal =
        email.contains('@dkshsmollan.com') || email.contains('@dksh.com');
    final isNz = email.contains('@dkshnz.com') ||
        email.contains('@storetrack.com') ||
        email.contains('@crossmark.biz');
    if (isMsal) {
      // open webview with url https://passwordreset.microsoftonline.com/
    }
    if (isNz) {
      context.read<ResetPasswordCubit>().resetPwNz(email: email);
    }
  }

  @override
  void initState() {
    super.initState();

    if (widget.email.isNotEmpty) _usernameController.text = widget.email;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent,
      appBar: const CustomAppBar(
        title: 'Forget Password',
        titleColor: Colors.white,
        backgroundColor: Colors.transparent,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
      ),
      body: BlocConsumer<ResetPasswordCubit, ResetPasswordState>(
        listener: (context, state) {
          if (state is ResetPasswordSuccess) {
            setState(() => _loading = false);
          } else if (state is ResetPasswordError) {
            setState(() => _loading = false);
            SnackBarService.error(
              context: context,
              message: state.message,
            );
          }
        },
        builder: (context, state) {
          if (state is ResetPasswordLoading) return const LoadingWidget();
          if (state is ResetPasswordError) {
            return RetryWidget(
              color: Colors.white,
              onRetry: _sendEmail,
            );
          }
          return Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF0077CC),
                  Color(0xFF005799),
                  Color(0xFF003366),
                ],
              ),
              image: DecorationImage(
                image: AssetImage(AppAssets.imgBg),
                fit: BoxFit.fill,
                opacity: 0.2,
              ),
            ),
            width: double.infinity,
            height: double.infinity,
            child: SafeArea(
              child: GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          const Text(
                            'Lost your password? \nPlease enter your username below and we`ll send you an email shortly.',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              height: 1.3,
                            ),
                          ),
                          const SizedBox(height: 56),
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.08),
                                  blurRadius: 24,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Form(
                                    key: _formKey,
                                    child: AppTextField(
                                      controller: _usernameController,
                                      validator: _validateUsername,
                                      keyboardType: TextInputType.emailAddress,
                                      label: "Email",
                                      textInputAction: TextInputAction.done,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 32),
                          AppButton(
                            text: _loading ? 'Please wait...' : 'Reset',
                            color: const Color(0xFFDE3814),
                            onPressed: _loading ? () {} : _handleReset,
                          ),
                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
