import 'dart:convert';

class LoginResponse {
  Data? data;

  LoginResponse({
    this.data,
  });

  factory LoginResponse.fromRawJson(String str) =>
      LoginResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  int? userId;
  String? token;
  bool? isPriceCheckerUser;
  bool? isAdminUniversal;
  bool? dayCheckIn;
  bool? pos;
  bool? openTasks;
  bool? vacancies;
  bool? premAutoSchedule;
  bool? premAvailability;
  bool? premAutoSchedule4Weeks;
  bool? watermarkImages;

  Data({
    this.userId,
    this.token,
    this.isPriceCheckerUser,
    this.isAdminUniversal,
    this.dayCheckIn,
    this.pos,
    this.openTasks,
    this.vacancies,
    this.premAutoSchedule,
    this.premAvailability,
    this.premAutoSchedule4Weeks,
    this.watermarkImages,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        userId: json["user_id"],
        token: json["token"],
        isPriceCheckerUser: json["is_price_checker_user"],
        isAdminUniversal: json["is_admin_universal"],
        dayCheckIn: json["day_check_in"],
        pos: json["pos"],
        openTasks: json["open_tasks"],
        vacancies: json["vacancies"],
        premAutoSchedule: json["prem_auto_schedule"],
        premAvailability: json["prem_availability"],
        premAutoSchedule4Weeks: json["prem_auto_schedule_4_weeks"],
        watermarkImages: json["watermark_images"],
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "token": token,
        "is_price_checker_user": isPriceCheckerUser,
        "is_admin_universal": isAdminUniversal,
        "day_check_in": dayCheckIn,
        "pos": pos,
        "open_tasks": openTasks,
        "vacancies": vacancies,
        "prem_auto_schedule": premAutoSchedule,
        "prem_availability": premAvailability,
        "prem_auto_schedule_4_weeks": premAutoSchedule4Weeks,
        "watermark_images": watermarkImages,
      };
}
