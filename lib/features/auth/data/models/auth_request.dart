class AuthRequest {
  final String email;
  final String? password;
  final String? sessionToken;

  AuthRequest({required this.email, this.password, this.sessionToken});

  factory AuthRequest.fromJson(Map<String, dynamic> json) => AuthRequest(
        email: json['email'] as String,
        password: json['password'] as String?,
        sessionToken: json['sessionToken'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'email': email,
        if (password != null) 'password': password,
        if (sessionToken != null) 'sessionToken': sessionToken,
      };
}
