import 'dart:convert';

class AuthResponse {
  Data? data;

  AuthResponse({
    this.data,
  });

  factory AuthResponse.fromRawJson(String str) =>
      AuthResponse.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory AuthResponse.fromJson(Map<String, dynamic> json) => AuthResponse(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  bool? isAuthenticated;
  String? authResponse;
  String? personalEmail;

  Data({
    this.isAuthenticated,
    this.authResponse,
    this.personalEmail,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        isAuthenticated: json["is_authenticated"],
        authResponse: json["auth_response"],
        personalEmail: json["personal_email"],
      );

  Map<String, dynamic> toJson() => {
        "is_authenticated": isAuthenticated,
        "auth_response": authResponse,
        "personal_email": personalEmail,
      };
}
