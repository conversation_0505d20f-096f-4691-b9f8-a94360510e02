import 'dart:convert';

class LoginRequest {
  String? azureJwt;
  String? deviceToken;
  String? devicePlatform;
  String? appversion;
  String? deviceuid;
  String? devicename;
  String? devicemodel;
  String? deviceversion;

  LoginRequest({
    this.azureJwt,
    this.deviceToken,
    this.devicePlatform,
    this.appversion,
    this.deviceuid,
    this.devicename,
    this.devicemodel,
    this.deviceversion,
  });

  factory LoginRequest.fromRawJson(String str) =>
      LoginRequest.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LoginRequest.fromJson(Map<String, dynamic> json) => LoginRequest(
        azureJwt: json["Azure_JWT"],
        deviceToken: json["device_token"],
        devicePlatform: json["device_platform"],
        appversion: json["appversion"],
        deviceuid: json["deviceuid"],
        devicename: json["devicename"],
        devicemodel: json["devicemodel"],
        deviceversion: json["deviceversion"],
      );

  Map<String, dynamic> toJson() => {
        "Azure_JWT": azureJwt,
        "device_token": deviceToken,
        "device_platform": devicePlatform,
        "appversion": appversion,
        "deviceuid": deviceuid,
        "devicename": devicename,
        "devicemodel": devicemodel,
        "deviceversion": deviceversion,
      };
}
