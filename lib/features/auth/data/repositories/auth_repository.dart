import 'package:storetrack_app/features/auth/data/models/auth_request.dart';
import 'package:storetrack_app/features/auth/data/models/auth_response.dart';
import 'package:storetrack_app/features/auth/data/models/login_request.dart';
import 'package:storetrack_app/features/auth/data/models/login_response.dart';

import '../../../../shared/models/result.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_data_source.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthDataSource dataSource;
  AuthRepositoryImpl({required this.dataSource});

  @override
  Future<Result<AuthResponse>> signInWithNz(AuthRequest authRequest) {
    return dataSource.authenticateWithNz(authRequest);
  }

  @override
  Future<Result<LoginResponse>> signIn(LoginRequest loginRequest) {
    return dataSource.login(loginRequest);
  }

  @override
  Future<Result<AuthResponse>> resetPasswordNz(String email) {
    return dataSource.resetPasswordNz(email);
  }
}
