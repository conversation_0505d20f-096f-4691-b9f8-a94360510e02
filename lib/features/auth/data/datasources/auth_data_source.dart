import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/auth/data/models/auth_request.dart';
import 'package:storetrack_app/features/auth/data/models/auth_response.dart';
import 'package:storetrack_app/features/auth/data/models/login_request.dart';
import 'package:storetrack_app/features/auth/data/models/login_response.dart';

import '../../../../core/network/api_client.dart';
import '../../../../shared/models/result.dart';

abstract class AuthDataSource {
  Future<Result<AuthResponse>> authenticateWithNz(AuthRequest authRequest);
  Future<Result<LoginResponse>> login(LoginRequest loginRequest);
  Future<Result<AuthResponse>> resetPasswordNz(String email);
}

class AuthDataSourceImpl implements AuthDataSource {
  final ApiClient networkClient;
  AuthDataSourceImpl({required this.networkClient});

  @override
  Future<Result<AuthResponse>> authenticateWithNz(
      AuthRequest authRequest) async {
    try {
      final response = await networkClient.instance.post(
        '/auth_storetrackNZ',
        data: {
          'email': authRequest.email,
          'password': authRequest.password,
        },
      );
      var authResponse = AuthResponse.fromJson(response.data);
      logger("haha11 ${authResponse.data?.isAuthenticated}}");
      if (authResponse.data?.isAuthenticated == false) {
        return Result.failure(authResponse.data?.authResponse ?? "Auth failed");
      } else {
        return Result.success(authResponse);
      }
    } catch (e) {
      return Result.failure("Auth failed: ${e.toString()}");
    }
  }

  @override
  Future<Result<LoginResponse>> login(LoginRequest loginRequest) async {
    try {
      var response = await networkClient.instance.post(
        '/login',
        data: loginRequest.toJson(),
      );
      return Result.success(LoginResponse.fromJson(response.data));
    } catch (e) {
      return Result.failure("Login failed: ${e.toString()}");
    }
  }

  @override
  Future<Result<AuthResponse>> resetPasswordNz(String email) async {
    try {
      final response = await networkClient.instance.post(
        '/password_resetNZ',
        data: {
          'email': email,
        },
      );
      var authResponse = AuthResponse.fromJson(response.data);
      logger("haha11 ${authResponse.data?.isAuthenticated}}");
      if (authResponse.data?.isAuthenticated == false) {
        return Result.failure(authResponse.data?.authResponse ?? "Auth failed");
      } else {
        return Result.success(authResponse);
      }
    } catch (e) {
      return Result.failure("Auth failed: ${e.toString()}");
    }
  }
}
