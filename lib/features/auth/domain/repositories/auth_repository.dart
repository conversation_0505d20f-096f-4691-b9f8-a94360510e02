import 'package:storetrack_app/features/auth/data/models/auth_request.dart';
import 'package:storetrack_app/features/auth/data/models/auth_response.dart';
import 'package:storetrack_app/features/auth/data/models/login_request.dart';
import 'package:storetrack_app/features/auth/data/models/login_response.dart';

import '../../../../shared/models/result.dart';

abstract class AuthRepository {
  Future<Result<AuthResponse>> signInWithNz(AuthRequest authRequest);
  Future<Result<LoginResponse>> signIn(LoginRequest loginRequest);
  Future<Result<AuthResponse>> resetPasswordNz(String email);
}
