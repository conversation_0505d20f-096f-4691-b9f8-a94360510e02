import 'package:storetrack_app/features/auth/data/models/auth_response.dart';
import 'package:storetrack_app/features/auth/domain/repositories/auth_repository.dart';

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';

class ResetPwNzUseCase implements UseCase<Result<AuthResponse>, String> {
  ResetPwNzUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Result<AuthResponse>> call(String email) async {
    return _repository.resetPasswordNz(email);
  }
}
