import 'package:storetrack_app/features/auth/data/models/auth_request.dart';
import 'package:storetrack_app/features/auth/data/models/auth_response.dart';
import 'package:storetrack_app/features/auth/domain/repositories/auth_repository.dart';

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';

class LoginNZUseCase implements UseCase<Result<AuthResponse>, AuthRequest> {
  LoginNZUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Result<AuthResponse>> call(AuthRequest request) async {
    return _repository.signInWithNz(request);
  }
}
