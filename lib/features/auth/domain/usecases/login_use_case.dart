import 'package:storetrack_app/features/auth/data/models/login_response.dart';
import 'package:storetrack_app/features/auth/domain/repositories/auth_repository.dart';

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/models/login_request.dart';

class LoginUseCase implements UseCase<Result<LoginResponse>, LoginRequest> {
  LoginUseCase(this._repository);

  final AuthRepository _repository;

  @override
  Future<Result<LoginResponse>> call(LoginRequest request) async {
    return _repository.signIn(request);
  }
}
