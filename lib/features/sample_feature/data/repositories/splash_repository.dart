// import 'package:gotcha_mfg_app/features/splash/data/data_sources/splash_remote_data_source.dart';
// import 'package:gotcha_mfg_app/features/splash/data/models/info_request.dart';
// import 'package:gotcha_mfg_app/features/splash/data/models/info_response.dart';
// import 'package:gotcha_mfg_app/features/splash/domain/repositories/splash_repository.dart';
// import 'package:gotcha_mfg_app/shared/models/result.dart';

// class SplashRepositoryImpl implements SplashRepository {
//   SplashRepositoryImpl(this._remoteDataSource);

//   final SplashRemoteDataSource _remoteDataSource;

//   @override
//   Future<Result<InfoResponse>> info(InfoRequest request) async {
//     return await _remoteDataSource.info(request);
//   }

//   // @override
//   // Future<Result<OnboardingResponse>> onboard(OnboardingRequest request) async {
//   //   return await _remoteDataSource.onboard(request);
//   // }
// }
