// import 'package:dio/dio.dart';
// import 'package:firebase_crashlytics/firebase_crashlytics.dart';
// import 'package:gotcha_mfg_app/features/splash/data/models/info_request.dart';
// import 'package:gotcha_mfg_app/features/splash/data/models/info_response.dart';
// import 'package:gotcha_mfg_app/shared/models/result.dart';

// /// Interface for splash remote data source
// abstract class SplashRemoteDataSource {
//   /// Onboard
//   // Future<Result<OnboardingResponse>> onboard(OnboardingRequest request);

//   /// Info
//   Future<Result<InfoResponse>> info(InfoRequest request);
// }

// /// Splash remote data source implementation
// class SplashRemoteDataSourceImpl implements SplashRemoteDataSource {
//   /// Constructor
//   SplashRemoteDataSourceImpl(this._dio);

//   final Dio _dio;

//   @override
//   // Future<Result<OnboardingResponse>> onboard(OnboardingRequest request) async {
//   //   try {
//   //     final response = await _dio.post('/app/onboarding/', data: request);
//   //     if (response.statusCode == 200) {
//   //       final data = response.data;
//   //       final onboardingResponse = OnboardingResponse.fromJson(data);
//   //       sl<TokenManager>().saveTokens(
//   //         accessToken: onboardingResponse.data?.accessToken ?? '',
//   //         refreshToken: onboardingResponse.data?.refreshToken ?? '',
//   //       );
//   //       return Result.success(onboardingResponse);
//   //     } else {
//   //       return Result.failure(
//   //           '${response.statusCode} - ${response.data['message']}');
//   //     }
//   //   } catch (e) {
//   //     return Result.failure(
//   //         'An error occurred during onboarding: ${e.toString()}');
//   //   }
//   // }

//   @override
//   Future<Result<InfoResponse>> info(InfoRequest request) async {
//     try {
//       final response = await _dio.post('/app/user/info/', data: request);
//       if (response.statusCode == 200) {
//         final data = response.data;
//         final infoResponse = InfoResponse.fromJson(data);

//         return Result.success(infoResponse);
//       } else {
//         return Result.failure(
//             '${response.statusCode} - ${response.data['message']}');
//       }
//     } catch (e) {
//       FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
//       return Result.failure(
//           'An error occurred during calling info: ${e.toString()}');
//     }
//   }
// }
