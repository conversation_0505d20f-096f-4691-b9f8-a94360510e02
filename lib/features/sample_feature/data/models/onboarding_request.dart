import 'dart:convert';

class OnboardingRequest {
  String? deviceId;
  List<Question>? questions;

  OnboardingRequest({
    this.deviceId,
    this.questions,
  });

  factory OnboardingRequest.fromRawJson(String str) =>
      OnboardingRequest.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory OnboardingRequest.fromJson(Map<String, dynamic> json) =>
      OnboardingRequest(
        deviceId: json["device_id"],
        questions: json["questions"] == null
            ? []
            : List<Question>.from(
                json["questions"]!.map((x) => Question.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "device_id": deviceId,
        "questions": questions == null
            ? []
            : List<dynamic>.from(questions!.map((x) => x.toJson())),
      };
}

class Question {
  String? questionId;
  String? answer;
  String? answerId;

  Question({
    this.questionId,
    this.answer,
    this.answerId,
  });

  factory Question.fromRaw<PERSON><PERSON>(String str) =>
      Question.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Question.fromJson(Map<String, dynamic> json) => Question(
        questionId: json["question_id"],
        answer: json["answer"],
        answerId: json["answer_id"],
      );

  Map<String, dynamic> toJson() => {
        "question_id": questionId,
        "answer": answer,
        "answer_id": answerId,
      };
}
