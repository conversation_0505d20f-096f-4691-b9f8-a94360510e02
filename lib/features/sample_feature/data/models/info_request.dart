import 'dart:convert';

class InfoRequest {
  String? deviceId;
  String? os;
  String? osVersion;
  String? token;
  String? fcmId;
  String? timezone;
  String? version;

  InfoRequest({
    this.deviceId,
    this.os,
    this.osVersion,
    this.token,
    this.fcmId,
    this.timezone,
    this.version,
  });

  factory InfoRequest.fromRawJson(String str) =>
      InfoRequest.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory InfoRequest.fromJson(Map<String, dynamic> json) => InfoRequest(
        deviceId: json["device_id"],
        os: json["os"],
        osVersion: json["os_version"],
        token: json["token"],
        fcmId: json["fcm_device_id"],
        timezone: json["time_zone"],
        version: json["version"],
      );

  Map<String, dynamic> toJson() => {
        "device_id": deviceId,
        "os": os,
        "os_version": osVersion,
        "token": token,
        "fcm_device_id": fcmId,
        "time_zone": timezone,
        "version": version,
      };
}
