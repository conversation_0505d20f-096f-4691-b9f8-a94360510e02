// import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
// import 'package:gotcha_mfg_app/features/splash/data/models/info_request.dart';
// import 'package:gotcha_mfg_app/features/splash/data/models/info_response.dart';
// import 'package:gotcha_mfg_app/features/splash/domain/repositories/splash_repository.dart';
// import 'package:gotcha_mfg_app/shared/models/result.dart';

// /// UseCase for info
// class InfoUseCase implements UseCase<Result<InfoResponse>, InfoRequest> {
//   /// Constructor
//   InfoUseCase(this._repository);

//   final SplashRepository _repository;

//   @override
//   Future<Result<InfoResponse>> call(InfoRequest request) async {
//     return _repository.info(request);
//   }
// }
