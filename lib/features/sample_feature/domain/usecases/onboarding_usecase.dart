// import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
// import 'package:gotcha_mfg_app/features/splash/data/models/onboarding_request.dart';
// import 'package:gotcha_mfg_app/features/splash/data/models/onboarding_response.dart';
// import 'package:gotcha_mfg_app/features/splash/domain/repositories/splash_repository.dart';
// import 'package:gotcha_mfg_app/shared/models/result.dart';

// class OnboardingUsecase
//     implements UseCase<Result<OnboardingResponse>,NoParams> {
//   /// Constructor
//   // OnboardingUsecase(this._repository);

//   final SplashRepository _repository;

//   // @override
//   // Future<Result<OnboardingResponse>> call(OnboardingRequest request) async {
//   //   return _repository.onboard(request);
//   // }
// }
