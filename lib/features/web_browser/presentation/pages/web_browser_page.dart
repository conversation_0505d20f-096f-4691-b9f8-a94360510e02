import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../../config/themes/app_colors.dart';

@RoutePage()
class WebBrowserPage extends StatefulWidget {
  final String url;

  const WebBrowserPage({
    super.key,
    required this.url,
  });

  @override
  State<WebBrowserPage> createState() => _WebBrowserPageState();
}

class _WebBrowserPageState extends State<WebBrowserPage> {
  late WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2.withOpacity(.9),
      appBar: CustomAppBar(
        title: 'Web Browser',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: AppColors.blackTint1),
            onPressed: () => _controller.reload(),
          ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryBlue,
              ),
            ),
        ],
      ),
    );
  }
}
