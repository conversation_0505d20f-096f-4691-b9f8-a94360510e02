import '../../domain/entities/tasks_response_entity.dart';
import '../models/task_detail_model.dart';
import 'dart:convert';

class TaskDetailMapper {
  static TaskDetailModel toModel(TaskDetail entity, int id) {
    return TaskDetailModel(
      id,
      isSynced: false,
      taskId: entity.taskId?.toInt(),
      projectId: entity.projectId?.toInt(),
      scheduleId: entity.scheduleId?.toInt(),
      sentToPayroll: entity.sentToPayroll,
      showKm: entity.showKm,
      storeId: entity.storeId?.toInt(),
      client: entity.client,
      clientId: entity.clientId?.toInt(),
      clientLogoUrl: entity.clientLogoUrl,
      storeGroup: entity.storeGroup,
      storeGroupId: entity.storeGroupId?.toInt(),
      storeName: entity.storeName,
      storeEmail: entity.storeEmail,
      minutes: entity.minutes?.toInt(),
      budget: entity.budget?.toInt(),
      originalbudget: entity.originalbudget?.toInt(),
      comment: entity.comment,
      claimableKms: entity.claimableKms?.toInt(),
      flightDuration: entity.flightDuration?.toInt(),
      pages: entity.pages?.toInt(),
      location: entity.location,
      suburb: entity.suburb,
      latitude: entity.latitude?.toDouble(),
      longitude: entity.longitude?.toDouble(),
      taskLatitude: entity.taskLatitude?.toDouble(),
      taskLongitude: entity.taskLongitude?.toDouble(),
      cycle: entity.cycle,
      cycleId: entity.cycleId?.toInt(),
      canDelete: entity.canDelete,
      scheduledTimeStamp: entity.scheduledTimeStamp,
      submissionTimeStamp: entity.submissionTimeStamp,
      expires: entity.expires,
      onTask: entity.onTask,
      phone: entity.phone,
      rangeStart: entity.rangeStart,
      rangeEnd: entity.rangeEnd,
      reOpened: entity.reOpened,
      reOpenedReason: entity.reOpenedReason,
      taskStatus: entity.taskStatus,
      warehousejobId: entity.warehousejobId?.toInt(),
      connoteUrl: entity.connoteUrl,
      posRequired: entity.posRequired,
      isPosMandatory: entity.isPosMandatory,
      posReceived: entity.posReceived,
      photoFolder: _mapPhotoFolders(entity.photoFolder),
      signatureFolder: _mapSignatureFolders(entity.signatureFolder),
      forms: _mapForms(entity.forms),
      posItems: _mapPosItems(entity.posItems),
      documents: _mapDocuments(entity.documents),
      taskalerts: _mapTaskalerts(entity.taskalerts),
      taskmembers: _mapTaskmembers(entity.taskmembers),
      modifiedTimeStampDocuments: entity.modifiedTimeStampDocuments,
      modifiedTimeStampForms: entity.modifiedTimeStampForms,
      modifiedTimeStampMembers: entity.modifiedTimeStampMembers,
      modifiedTimeStampTask: entity.modifiedTimeStampTask,
      modifiedTimeStampPhotos: entity.modifiedTimeStampPhotos,
      modifiedTimeStampSignatures: entity.modifiedTimeStampSignatures,
      modifiedTimeStampSignaturetypes: entity.modifiedTimeStampSignaturetypes,
      posSentTo: entity.posSentTo,
      posSentToEmail: entity.posSentToEmail,
      modifiedTimeStampPhototypes: entity.modifiedTimeStampPhototypes,
      taskCommencementTimeStamp: entity.taskCommencementTimeStamp,
      taskStoppedTimeStamp: entity.taskStoppedTimeStamp,
      teamlead: entity.teamlead?.toInt(),
      followupTasks: _mapFollowupTasks(entity.followupTasks),
      stocktake: _mapStocktake(entity.stocktake),
      taskNote: entity.taskNote,
      disallowReschedule: entity.disallowReschedule,
      photoResPerc: entity.photoResPerc?.toInt(),
      liveImagesOnly: entity.liveImagesOnly,
      timeSchedule: entity.timeSchedule,
      scheduleTypeId: entity.scheduleTypeId?.toInt(),
      showFollowupIconMulti: entity.showFollowupIconMulti,
      followupSelectedMulti: entity.followupSelectedMulti,
      regionId: entity.regionId?.toInt(),
      isOpen: entity.isOpen,
      taskCount: entity.taskCount?.toInt(),
      ctFormsTotalCnt: entity.ctFormsTotalCnt?.toInt(),
      ctFormsCompletedCnt: entity.ctFormsCompletedCnt?.toInt(),
      preftime: entity.preftime,
      sendTo: entity.sendTo,
    );
  }

  static TaskDetail toEntity(TaskDetailModel model) {
    return TaskDetail(
      taskId: model.taskId,
      projectId: model.projectId,
      scheduleId: model.scheduleId,
      sentToPayroll: model.sentToPayroll,
      showKm: model.showKm,
      storeId: model.storeId,
      client: model.client,
      clientId: model.clientId,
      clientLogoUrl: model.clientLogoUrl,
      storeGroup: model.storeGroup,
      storeGroupId: model.storeGroupId,
      storeName: model.storeName,
      storeEmail: model.storeEmail,
      minutes: model.minutes,
      budget: model.budget,
      originalbudget: model.originalbudget,
      comment: model.comment,
      claimableKms: model.claimableKms,
      flightDuration: model.flightDuration,
      pages: model.pages,
      location: model.location,
      suburb: model.suburb,
      latitude: model.latitude,
      longitude: model.longitude,
      taskLatitude: model.taskLatitude,
      taskLongitude: model.taskLongitude,
      cycle: model.cycle,
      cycleId: model.cycleId,
      canDelete: model.canDelete,
      scheduledTimeStamp: model.scheduledTimeStamp,
      submissionTimeStamp: model.submissionTimeStamp,
      expires: model.expires,
      onTask: model.onTask,
      phone: model.phone,
      rangeStart: model.rangeStart,
      rangeEnd: model.rangeEnd,
      reOpened: model.reOpened,
      reOpenedReason: model.reOpenedReason,
      taskStatus: model.taskStatus,
      warehousejobId: model.warehousejobId,
      connoteUrl: model.connoteUrl,
      posRequired: model.posRequired,
      isPosMandatory: model.isPosMandatory,
      posReceived: model.posReceived,
      photoFolder: _mapPhotoFoldersToEntity(model.photoFolder),
      signatureFolder: _mapSignatureFoldersToEntity(model.signatureFolder),
      forms: _mapFormsToEntity(model.forms),
      posItems: _mapPosItemsToEntity(model.posItems),
      documents: _mapDocumentsToEntity(model.documents),
      taskalerts: _mapTaskalertsToEntity(model.taskalerts),
      taskmembers: _mapTaskmembersToEntity(model.taskmembers),
      modifiedTimeStampDocuments: model.modifiedTimeStampDocuments,
      modifiedTimeStampForms: model.modifiedTimeStampForms,
      modifiedTimeStampMembers: model.modifiedTimeStampMembers,
      modifiedTimeStampTask: model.modifiedTimeStampTask,
      modifiedTimeStampPhotos: model.modifiedTimeStampPhotos,
      modifiedTimeStampSignatures: model.modifiedTimeStampSignatures,
      modifiedTimeStampSignaturetypes: model.modifiedTimeStampSignaturetypes,
      posSentTo: model.posSentTo,
      posSentToEmail: model.posSentToEmail,
      modifiedTimeStampPhototypes: model.modifiedTimeStampPhototypes,
      taskCommencementTimeStamp: model.taskCommencementTimeStamp,
      taskStoppedTimeStamp: model.taskStoppedTimeStamp,
      teamlead: model.teamlead,
      followupTasks: _mapFollowupTasksToEntity(model.followupTasks),
      stocktake: _mapStocktakeToEntity(model.stocktake),
      taskNote: model.taskNote,
      disallowReschedule: model.disallowReschedule,
      photoResPerc: model.photoResPerc,
      liveImagesOnly: model.liveImagesOnly,
      timeSchedule: model.timeSchedule,
      scheduleTypeId: model.scheduleTypeId,
      showFollowupIconMulti: model.showFollowupIconMulti,
      followupSelectedMulti: model.followupSelectedMulti,
      regionId: model.regionId,
      isOpen: model.isOpen,
      taskCount: model.taskCount,
      ctFormsTotalCnt: model.ctFormsTotalCnt,
      ctFormsCompletedCnt: model.ctFormsCompletedCnt,
      preftime: model.preftime,
      sendTo: model.sendTo,
    );
  }

  // Helper methods for mapping nested objects
  static List<PhotoFolderModel> _mapPhotoFolders(List<PhotoFolder>? folders) {
    if (folders == null) return [];
    return folders
        .map((folder) => PhotoFolderModel(
              name: folder.folderName,
              photos: folder.photos
                      ?.map((photo) => jsonEncode({
                            'photoId': photo.photoId,
                            'photoUrl': photo.photoUrl,
                            'thumbnailUrl': photo.thumbnailUrl,
                            'caption': photo.caption,
                          }))
                      .toList() ??
                  [],
            ))
        .toList();
  }

  static List<PhotoFolder> _mapPhotoFoldersToEntity(
      Iterable<PhotoFolderModel> models) {
    return models
        .map((model) => PhotoFolder(
              folderName: model.name,
              photos: model.photos.map((photoJson) {
                final photoData = jsonDecode(photoJson);
                return Photo(
                  photoId: photoData['photoId'],
                  photoUrl: photoData['photoUrl'],
                  thumbnailUrl: photoData['thumbnailUrl'],
                  caption: photoData['caption'],
                );
              }).toList(),
            ))
        .toList();
  }

  static List<SignatureFolderModel> _mapSignatureFolders(
      List<SignatureFolder>? folders) {
    if (folders == null) return [];
    return folders
        .map((folder) => SignatureFolderModel(
              name: folder.folderName,
              signatures: folder.signatures
                      ?.map((sig) => jsonEncode({
                            'signatureId': sig.signatureId,
                            'signatureUrl': sig.signatureUrl,
                            'signedBy': sig.signedBy,
                          }))
                      .toList() ??
                  [],
            ))
        .toList();
  }

  static List<SignatureFolder> _mapSignatureFoldersToEntity(
      Iterable<SignatureFolderModel> models) {
    return models
        .map((model) => SignatureFolder(
              folderName: model.name,
              signatures: model.signatures.map((sigJson) {
                final sigData = jsonDecode(sigJson);
                return Signature(
                  signatureId: sigData['signatureId'],
                  signatureUrl: sigData['signatureUrl'],
                  signedBy: sigData['signedBy'],
                );
              }).toList(),
            ))
        .toList();
  }

  static List<FormModel> _mapForms(List<Form>? forms) {
    if (forms == null) return [];
    return forms
        .map((form) => FormModel(
              formId: form.formId?.toInt(),
              formName: form.formName,
              formType:
                  'form', // Default form type since entity doesn't have this field
              answers: form.questionAnswers
                      ?.map((answer) => QuestionAnswerModel(
                            questionId: answer.questionId?.toInt(),
                            answer: answer.measurementTextResult,
                            comment: answer.questionPartMultiId,
                          ))
                      .toList() ??
                  [],
            ))
        .toList();
  }

  static List<Form> _mapFormsToEntity(Iterable<FormModel> models) {
    return models
        .map((model) => Form(
              formId: model.formId,
              formName: model.formName,
              questionAnswers: model.answers
                  .map((answer) => QuestionAnswer(
                        questionId: answer.questionId,
                        measurementTextResult: answer.answer,
                        questionPartMultiId: answer.comment,
                      ))
                  .toList(),
            ))
        .toList();
  }

  static List<PosItemModel> _mapPosItems(List<PosItem>? items) {
    if (items == null) return [];
    return items
        .map((item) => PosItemModel(
              itemId: 0, // PosItem doesn't have itemId, using default
              itemName: item.itemName,
              quantity: item.itemAmount?.toInt(),
            ))
        .toList();
  }

  static List<PosItem> _mapPosItemsToEntity(Iterable<PosItemModel> models) {
    return models
        .map((model) => PosItem(
              itemName: model.itemName,
              itemAmount: model.quantity,
            ))
        .toList();
  }

  static List<DocumentModel> _mapDocuments(List<Document>? documents) {
    if (documents == null) return [];
    return documents
        .map((doc) => DocumentModel(
              documentId: doc.documentId?.toInt(),
              documentName: doc.documentName,
              documentUrl:
                  doc.documentIconLink, // Using documentIconLink as URL
            ))
        .toList();
  }

  static List<Document> _mapDocumentsToEntity(Iterable<DocumentModel> models) {
    return models
        .map((model) => Document(
              documentId: model.documentId,
              documentName: model.documentName,
              documentIconLink: model.documentUrl,
            ))
        .toList();
  }

  static List<TaskalertModel> _mapTaskalerts(List<Taskalert>? alerts) {
    if (alerts == null) return [];
    return alerts
        .map((alert) => TaskalertModel(
              messageId: alert.messageId?.toInt(),
              schedulepeopleid: alert.schedulepeopleid?.toInt(),
              subject: alert.subject,
              message: alert.message,
            ))
        .toList();
  }

  static List<Taskalert> _mapTaskalertsToEntity(
      Iterable<TaskalertModel> models) {
    return models
        .map((model) => Taskalert(
              messageId: model.messageId,
              schedulepeopleid: model.schedulepeopleid,
              subject: model.subject,
              message: model.message,
            ))
        .toList();
  }

  static List<TaskmemberModel> _mapTaskmembers(List<Taskmember>? members) {
    if (members == null) return [];
    return members
        .map((member) => TaskmemberModel(
              fullname: member.fullname,
              teamLead: member.teamLead?.toInt(),
              email: member.email,
              scheduleId: member.scheduleId?.toInt(),
              taskId: member.taskId?.toInt(),
            ))
        .toList();
  }

  static List<Taskmember> _mapTaskmembersToEntity(
      Iterable<TaskmemberModel> models) {
    return models
        .map((model) => Taskmember(
              fullname: model.fullname,
              teamLead: model.teamLead,
              email: model.email,
              scheduleId: model.scheduleId,
              taskId: model.taskId,
            ))
        .toList();
  }

  static List<FollowupTaskModel> _mapFollowupTasks(List<FollowupTask>? tasks) {
    if (tasks == null) return [];
    return tasks
        .map((task) => FollowupTaskModel(
              followupTaskId: task.selectedFollowupItemId?.toInt(),
              description: task.selectedFollowupItem,
              dueDate: task.selectedVisitDate,
            ))
        .toList();
  }

  static List<FollowupTask> _mapFollowupTasksToEntity(
      Iterable<FollowupTaskModel> models) {
    return models
        .map((model) => FollowupTask(
              selectedFollowupItemId: model.followupTaskId,
              selectedFollowupItem: model.description,
              selectedVisitDate: model.dueDate,
            ))
        .toList();
  }

  static List<StocktakeModel> _mapStocktake(List<Stocktake>? stocktake) {
    if (stocktake == null) return [];
    return stocktake
        .map((item) => StocktakeModel(
              taskId: item.taskId?.toInt(),
              itemId: item.itemId?.toInt(),
              itemCode: item.itemCode,
              itemName: item.itemName,
              itemGroup: item.itemGroup,
              imageUrl: item.imageUrl,
              itemLocation: item.itemLocation,
              itemQty: item.itemQty?.toInt(),
            ))
        .toList();
  }

  static List<Stocktake> _mapStocktakeToEntity(
      Iterable<StocktakeModel> models) {
    return models
        .map((model) => Stocktake(
              taskId: model.taskId,
              itemId: model.itemId,
              itemCode: model.itemCode,
              itemName: model.itemName,
              itemGroup: model.itemGroup,
              imageUrl: model.imageUrl,
              itemLocation: model.itemLocation,
              itemQty: model.itemQty,
            ))
        .toList();
  }
}
