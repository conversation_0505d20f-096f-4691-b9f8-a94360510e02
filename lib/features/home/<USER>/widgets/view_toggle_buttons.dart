import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

class ViewToggleButtons extends StatelessWidget implements PreferredSizeWidget {
  final String selectedButton;
  final TabController tabController;
  final Function(String) onButtonPressed;

  const ViewToggleButtons({
    super.key,
    required this.selectedButton,
    required this.tabController,
    required this.onButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(48),
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // Tab bar with All, This Week, Next Week, and Overdue buttons
            Expanded(
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xFFE0E0E0),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    // All button
                    _buildToggleButton(
                      context,
                      'All',
                      'all',
                      const BorderRadius.only(
                        topLeft: Radius.circular(8),
                        bottomLeft: Radius.circular(8),
                      ),
                    ),
                    // This Week button
                    _buildToggleButton(
                      context,
                      'This week',
                      'this_week',
                      BorderRadius.zero,
                    ),
                    // Next Week button
                    _buildToggleButton(
                      context,
                      'Next week',
                      'next_week',
                      BorderRadius.zero,
                    ),
                    // Overdue button
                    _buildToggleButton(
                      context,
                      'Overdue',
                      'overdue',
                      const BorderRadius.only(
                        topRight: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleButton(
    BuildContext context,
    String text,
    String buttonKey,
    BorderRadius borderRadius,
  ) {
    final bool isSelected = selectedButton == buttonKey;

    return Expanded(
      child: ElevatedButton(
        onPressed: () => onButtonPressed(buttonKey),
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor:
              isSelected ? AppColors.primaryBlue : const Color(0xFFF5F5F5),
          foregroundColor: isSelected ? Colors.white : AppColors.blackTint1,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius,
            side: const BorderSide(
              color: Colors.grey,
              width: 0,
            ),
          ),
          padding: EdgeInsets.zero,
        ),
        child: Text(
          text,
          style: Theme.of(context)
              .textTheme
              .montserratNavigationPrimaryMedium
              .copyWith(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48);
}
