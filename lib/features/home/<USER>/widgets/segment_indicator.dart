import 'package:flutter/material.dart';

class SegmentedProgressIndicator extends StatelessWidget {
  final double progress; // Progress value between 0.0 and 1.0
  final int segments; // Number of segments to display
  final Color activeColor; // Color for filled segments
  final Color backgroundColor; // Color for unfilled segments
  final Color dividerColor; // Color for the divider
  final double height; // Height of the progress bar
  final double borderRadius; // Border radius for the segments
  final double dividerWidth; // Width of the divider
  final double segmentSpacing; // Space between segments
  final double? totalWidth; // Optional total width constraint

  const SegmentedProgressIndicator({
    super.key,
    required this.progress,
    this.segments = 10,
    this.activeColor = Colors.blue,
    this.backgroundColor = const Color(0xFFE0E0E0), // Colors.grey[300]
    this.dividerColor = Colors.black,
    this.height = 10.0,
    this.borderRadius = 10.0, // Increased to ensure proper curved edges
    this.dividerWidth = 2.0,
    this.segmentSpacing = 1.0, // Default no spacing between segments
    this.totalWidth,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate which segment the divider should appear in
    final int activeSegmentIndex = (progress * segments).floor();

    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: SizedBox(
        height: height,
        width: totalWidth,
        child: LayoutBuilder(builder: (context, constraints) {
          final availableWidth = totalWidth ?? constraints.maxWidth;
          final totalSpacingWidth = segmentSpacing * (segments - 1);
          final segmentWidth = (availableWidth - totalSpacingWidth) / segments;

          return Row(
            children: List.generate(segments, (index) {
              // Determine if this segment should be filled
              final bool isFilled = index < activeSegmentIndex;

              // Determine if this is the active segment (where the divider is)
              final bool isActiveSegment = index == activeSegmentIndex;

              // Determine if this is the first or last segment for border radius
              final bool isFirstSegment = index == 0;
              final bool isLastSegment = index == segments - 1;

              // Border radius configuration
              BorderRadiusGeometry? segmentBorderRadius;

              if (isFirstSegment && isLastSegment) {
                segmentBorderRadius = BorderRadius.circular(borderRadius);
              } else if (isFirstSegment) {
                segmentBorderRadius = BorderRadius.horizontal(
                    left: Radius.circular(borderRadius));
              } else if (isLastSegment) {
                segmentBorderRadius = BorderRadius.horizontal(
                    right: Radius.circular(borderRadius));
              }

              // Add spacing between segments (except before the first one)
              if (index > 0 && segmentSpacing > 0) {
                return Row(
                  children: [
                    SizedBox(width: segmentSpacing),
                    _buildSegment(
                      index: index,
                      isActiveSegment: isActiveSegment,
                      isFilled: isFilled,
                      segmentWidth: segmentWidth,
                      activeSegmentIndex: activeSegmentIndex,
                      segmentBorderRadius: segmentBorderRadius,
                      isFirstSegment: isFirstSegment,
                      isLastSegment: isLastSegment,
                    ),
                  ],
                );
              }

              return _buildSegment(
                index: index,
                isActiveSegment: isActiveSegment,
                isFilled: isFilled,
                segmentWidth: segmentWidth,
                activeSegmentIndex: activeSegmentIndex,
                segmentBorderRadius: segmentBorderRadius,
                isFirstSegment: isFirstSegment,
                isLastSegment: isLastSegment,
              );
            }),
          );
        }),
      ),
    );
  }

  Widget _buildSegment({
    required int index,
    required bool isActiveSegment,
    required bool isFilled,
    required double segmentWidth,
    required int activeSegmentIndex,
    BorderRadiusGeometry? segmentBorderRadius,
    required bool isFirstSegment,
    required bool isLastSegment,
  }) {
    // For the segment with the divider
    if (isActiveSegment) {
      final double segmentProgress = (progress * segments) - activeSegmentIndex;

      return SizedBox(
        width: segmentWidth,
        child: Stack(
          children: [
            // Background
            Container(
              width: segmentWidth,
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: segmentBorderRadius,
              ),
            ),

            // Filled portion
            Container(
              width: segmentWidth * segmentProgress,
              decoration: BoxDecoration(
                color: activeColor,
                borderRadius: isFirstSegment
                    ? BorderRadius.horizontal(
                        left: Radius.circular(borderRadius))
                    : null,
              ),
            ),

            // Black divider
            Positioned(
              left: segmentWidth * segmentProgress - dividerWidth / 2,
              top: 0,
              bottom: 0,
              child: Container(
                width: dividerWidth,
                color: dividerColor,
              ),
            ),
          ],
        ),
      );
    }

    // For regular segments
    return Container(
      width: segmentWidth,
      decoration: BoxDecoration(
        color: isFilled ? activeColor : backgroundColor,
        borderRadius: segmentBorderRadius,
      ),
    );
  }
}
