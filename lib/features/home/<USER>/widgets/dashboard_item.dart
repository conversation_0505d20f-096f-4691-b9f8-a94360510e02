import 'package:flutter/material.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

import 'segment_indicator.dart';

class DashboardItem extends StatelessWidget {
  final String title;
  final Widget? icon;
  final String? value;
  final Widget? customContent;
  final double? progress;
  final VoidCallback? ontap;

  const DashboardItem({
    super.key,
    required this.title,
    this.ontap,
    this.icon,
    this.value,
    this.customContent,
    this.progress,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return GestureDetector(
      onTap: ontap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 0,
            horizontal: 2,
          ),
          child: SizedBox(
            height: double.infinity,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 8, top: 16),
                  child: Text(
                    title,
                    style: textTheme.montserratTitleXxsmall,
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                if (customContent != null)
                  customContent!
                else if (value != null && icon != null)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      icon!,
                      const SizedBox(width: 8),
                      Text(
                        value!,
                        style: textTheme.montserratBold.copyWith(
                            fontSize: 24, fontWeight: FontWeight.w600),
                      ),
                    ],
                  )
                else if (value != null)
                  Text(
                    value!,
                    style: textTheme.montserratBold
                        .copyWith(fontSize: 14, fontWeight: FontWeight.w600),
                  )
                else if (icon != null)
                  icon!,
                if (progress != null) ...[
                  const SizedBox(height: 4),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // Use the available width from constraints for the SegmentedProgressIndicator
                        final progressWidth = constraints.maxWidth;

                        // Calculate position based on the width of the SegmentedProgressIndicator
                        final position = progressWidth * progress!;

                        // Center the line (line width is 1.5)
                        final adjustedPosition = position - 0.75;

                        return Stack(
                          clipBehavior: Clip.none,
                          children: [
                            // Base progress indicator
                            SegmentedProgressIndicator(
                              progress: progress!,
                              totalWidth: progressWidth,
                              activeColor: Colors.blue,
                              backgroundColor:
                                  Colors.grey[300] ?? const Color(0xFFE0E0E0),
                              dividerColor: Colors.black,
                              height: 10,
                              segments: 10,
                              borderRadius:
                                  10, // Increased border radius for more curved edges
                            ),

                            // Vertical line
                            Positioned(
                              left:
                                  adjustedPosition, // Using adjusted position to center the line
                              top:
                                  -8, // Increased top position to make the line longer
                              bottom: 0, // Extend to the bottom of the stack
                              child: Container(
                                width: 1.5,
                                color: Colors.black,
                              ),
                            ),

                            // Percentage text positioned in the middle of the vertical line
                            Positioned(
                              left: adjustedPosition,
                              top:
                                  -25, // Position in the middle of the vertical line
                              child: Container(
                                // Transform to center the text on the line
                                transform: Matrix4.translationValues(-14, 0, 0),
                                child: Text(
                                  '${(progress! * 100).toInt()}%',
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
