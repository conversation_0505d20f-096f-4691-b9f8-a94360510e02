import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class TaskOptionsBottomSheet extends StatelessWidget {
  final TaskDetail task;

  const TaskOptionsBottomSheet({
    super.key,
    required this.task,
  });

  static Future<void> show({
    required BuildContext context,
    required TaskDetail task,
  }) async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: TaskOptionsBottomSheet(task: task),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Handle bar at top
        Container(
          margin: const EdgeInsets.only(top: 8),
          width: 40,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey.shade400,
            borderRadius: BorderRadius.circular(2),
          ),
        ),

        // Options list
        _buildOptionItem(
          context: context,
          title: 'Forms',
          customIcon: _buildCustomIcon(Icons.check_box_outlined),
          onTap: () {
            Navigator.pop(context);
            // Handle forms action
          },
        ),

        _buildDivider(),

        _buildOptionItem(
          context: context,
          title: 'POS',
          customIcon: _buildCustomIcon(Icons.inventory_2_outlined),
          onTap: () {
            Navigator.pop(context);
            // Handle POS action
          },
        ),

        _buildDivider(),

        _buildOptionItem(
          context: context,
          title: 'Notes',
          customIcon: _buildCustomIcon(Icons.chat_bubble_outline),
          onTap: () {
            Navigator.pop(context);
            // Handle Notes action
          },
        ),

        _buildDivider(),

        _buildOptionItem(
          context: context,
          title: 'Directions',
          customIcon: _buildCustomIcon(Icons.map_outlined),
          onTap: () {
            Navigator.pop(context);
            // Handle Directions action
          },
        ),

        _buildDivider(),

        _buildOptionItem(
          context: context,
          title: 'Store info',
          customIcon: _buildCustomIcon(Icons.store_outlined),
          onTap: () {
            Navigator.pop(context);
            // Handle Store info action
          },
        ),

        _buildDivider(),

        _buildOptionItem(
          context: context,
          title: 'Store history',
          customIcon: _buildCustomIcon(Icons.history),
          onTap: () {
            Navigator.pop(context);
            // Handle Store history action
          },
        ),

        _buildDivider(),

        _buildOptionItem(
          context: context,
          title: 'Task assistance',
          customIcon: _buildCustomIcon(Icons.chat_outlined),
          onTap: () {
            Navigator.pop(context);
            // Handle Task assistance action
          },
        ),

        _buildDivider(),

        _buildOptionItem(
          context: context,
          title: 'Complete task',
          customIcon:
              _buildCustomIcon(Icons.check, color: AppColors.primaryBlue),
          textColor: AppColors.primaryBlue,
          onTap: () {
            Navigator.pop(context);
            // Handle Complete task action
          },
        ),

        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildCustomIcon(IconData icon, {Color? color}) {
    return Container(
      width: 28,
      height: 28,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade400, width: 1.5),
      ),
      child: Center(
        child: Icon(
          icon,
          size: 18,
          color: color ?? Colors.black,
        ),
      ),
    );
  }

  Widget _buildOptionItem({
    required BuildContext context,
    required String title,
    required Widget customIcon,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                    color: textColor ?? Colors.black,
                  ),
            ),
            const Spacer(),
            customIcon,
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return const Divider(
      height: 1,
      thickness: 1,
      indent: 16,
      endIndent: 16,
    );
  }
}
