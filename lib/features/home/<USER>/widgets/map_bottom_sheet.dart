import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';

class MapBottomSheet extends StatefulWidget {
  final ScrollController? scrollController;

  const MapBottomSheet({
    super.key,
    this.scrollController,
  });

  @override
  State<MapBottomSheet> createState() => _MapBottomSheetState();
}

class _MapBottomSheetState extends State<MapBottomSheet> {
  bool _isOptimizeRoute = true;
  bool _isShowAllStores = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: ListView(
        controller: widget.scrollController,
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        children: [
          // Handle bar at top - made more prominent for better dragging
          GestureDetector(
            onVerticalDragUpdate: (details) {
              // This helps make the drag handle more responsive
              if (widget.scrollController != null) {
                widget.scrollController!.jumpTo(
                  widget.scrollController!.offset - details.delta.dy,
                );
              }
            },
            child: Center(
              child: Container(
                margin: const EdgeInsets.symmetric(vertical: 16),
                width: 40,
                height: 5,
                decoration: BoxDecoration(
                  color: Colors.grey.shade400,
                  borderRadius: BorderRadius.circular(2.5),
                ),
              ),
            ),
          ),

          // Title
          Text(
            'Map settings',
            style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const Gap(16),

          // Optimize route toggle
          _buildToggleOption(
            title: 'Optimize route',
            subtitle: 'Show the most efficient route between stores',
            isEnabled: _isOptimizeRoute,
            onChanged: (value) {
              setState(() {
                _isOptimizeRoute = value;
              });
            },
          ),

          const Divider(),

          // Show all stores toggle
          _buildToggleOption(
            title: 'Show all stores',
            subtitle: 'Display all stores on the map',
            isEnabled: _isShowAllStores,
            onChanged: (value) {
              setState(() {
                _isShowAllStores = value;
              });
            },
          ),

          const Divider(),

          // Date selection
          _buildDateSelection(),

          const Gap(16),

          // Apply button
          SizedBox(
            width: double.infinity,
            child: AppButton(
              text: 'Apply',
              color: AppColors.primaryBlue,
              onPressed: () {
                // Instead of closing the sheet, collapse it to minimum size
                if (widget.scrollController != null) {
                  widget.scrollController!.animateTo(
                    0,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeOut,
                  );
                }
              },
              height: 48,
            ),
          ),
          const Gap(16),
        ],
      ),
    );
  }

  Widget _buildToggleOption({
    required String title,
    required String subtitle,
    required bool isEnabled,
    required Function(bool) onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style:
                      Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                ),
                const Gap(4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.montserratBold.copyWith(
                        color: Colors.grey.shade600,
                      ),
                ),
              ],
            ),
          ),
          Switch(
            value: isEnabled,
            onChanged: onChanged,
            activeColor: AppColors.primaryBlue,
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date',
          style: Theme.of(context).textTheme.montserratTitleSmall.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        const Gap(8),
        Row(
          children: [
            Expanded(
              child: _buildDateButton(
                label: 'Today',
                isSelected: true,
              ),
            ),
            const Gap(8),
            Expanded(
              child: _buildDateButton(
                label: 'Tomorrow',
                isSelected: false,
              ),
            ),
            const Gap(8),
            Expanded(
              child: _buildDateButton(
                label: 'Custom',
                isSelected: false,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateButton({
    required String label,
    required bool isSelected,
  }) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primaryBlue : Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          label,
          style: Theme.of(context).textTheme.montserratMedium.copyWith(
                color: isSelected ? Colors.white : Colors.black,
                fontWeight: FontWeight.w500,
              ),
        ),
      ),
    );
  }
}
