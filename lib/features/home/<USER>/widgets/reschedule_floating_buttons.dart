import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class RescheduleFloatingButtons extends StatelessWidget {
  final bool isCheckboxMode;
  final List<TaskDetail> selectedItems;
  final bool areAllItemsSelected;
  final VoidCallback onClose;
  final VoidCallback onSelectAll;
  final VoidCallback onReschedule;

  const RescheduleFloatingButtons({
    super.key,
    required this.isCheckboxMode,
    required this.selectedItems,
    required this.areAllItemsSelected,
    required this.onClose,
    required this.onSelectAll,
    required this.onReschedule,
  });

  @override
  Widget build(BuildContext context) {
    if (!isCheckboxMode || selectedItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: AppColors.midGrey,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.all(8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: onClose,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.close_rounded,
                color: AppColors.black,
              ),
            ),
          ),
          const Gap(8),
          SizedBox(
            width: 96,
            child: AppButton(
              text: areAllItemsSelected ? "Deselect all" : "Select all",
              color: Colors.white,
              textColor: AppColors.black,
              onPressed: onSelectAll,
              height: 40,
            ),
          ),
          const Gap(8),
          SizedBox(
            width: 96,
            child: AppButton(
              text: "Reschedule",
              color: AppColors.primaryBlue,
              onPressed: onReschedule,
              height: 40,
            ),
          ),
        ],
      ),
    );
  }
}
