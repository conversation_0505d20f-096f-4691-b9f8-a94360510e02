import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as schedule;

class DateTaskHeader extends StatelessWidget {
  final DateTime date;
  final List<schedule.TaskDetail> tasksForDate;
  final String Function(List<schedule.TaskDetail>) calculateTotalHours;

  const DateTaskHeader({
    super.key,
    required this.date,
    required this.tasksForDate,
    required this.calculateTotalHours,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    // Check if this date is today
    final bool isToday = date.day == DateTime.now().day &&
        date.month == DateTime.now().month &&
        date.year == DateTime.now().year;

    // Check if this date is a weekend
    final bool isWeekend = date.weekday >= 6; // Saturday or Sunday

    return Container(
      width: double.infinity,
      color: tasksForDate.isNotEmpty ? Colors.transparent : Colors.transparent,
      padding: EdgeInsets.symmetric(
          horizontal: 16.0, vertical: tasksForDate.isNotEmpty ? 2 : 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Date with day name
          Text(
            DateFormat('EEE d MMM').format(date),
            style: textTheme.montserratTitleSmall.copyWith(
                color: isToday
                    ? AppColors.primaryBlue
                    : isWeekend
                        ? Colors.grey
                        : isWeekend
                            ? Colors.grey
                            : tasksForDate.isEmpty
                                ? AppColors.primaryBlue
                                : Colors.black),
          ),

          // Task count with blue color for non-weekend days
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: tasksForDate.isEmpty
                ? Text(
                    'No tasks',
                    style: textTheme.montserratTitleExtraSmall
                        .copyWith(color: AppColors.primaryBlue),
                  )
                : Row(
                    children: [
                      Image.asset(AppAssets.appbarRecentTime,
                          scale: 4, color: Colors.black),
                      const Gap(4),
                      Text(
                        calculateTotalHours(tasksForDate),
                        style: textTheme.montserratTitleExtraSmall.copyWith(
                            color: isWeekend
                                ? Colors.grey
                                : tasksForDate.isEmpty
                                    ? AppColors.primaryBlue
                                    : Colors.black),
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }
}
