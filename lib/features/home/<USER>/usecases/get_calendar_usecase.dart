import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';

import '../../../../core/usecase/usecase.dart';
import '../../../../shared/models/result.dart';
import '../../data/repositories/home_repository.dart';

class GetCalendarUseCase
    implements UseCase<Result<CalendarResponseEntity>, GetCalendarParams> {
  final HomeRepository _repository;
  GetCalendarUseCase(this._repository);

  @override
  Future<Result<CalendarResponseEntity>> call(GetCalendarParams request) async {
    return _repository.getCalendarData(request);
  }
}

class GetCalendarParams {
  final String token;
  final String userId;

  GetCalendarParams({
    required this.token,
    required this.userId,
  });
}
