import 'package:storetrack_app/core/usecase/usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';
import '../../data/repositories/home_repository.dart';
import '../entities/submit_report_request_entity.dart';
import '../entities/submit_report_response_entity.dart';

class SubmitReportUseCase extends UseCase<Result<SubmitReportResponseEntity>,
    SubmitReportRequestEntity> {
  final HomeRepository _repository;
  SubmitReportUseCase(this._repository);

  @override
  Future<Result<SubmitReportResponseEntity>> call(
      SubmitReportRequestEntity request) async {
    return _repository.submitReport(request);
  }
}
