import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_constants.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

@RoutePage()
class MorePage extends StatefulWidget {
  const MorePage({super.key});

  @override
  State<MorePage> createState() => _MorePageState();
}

class _MorePageState extends State<MorePage>
    with SingleTickerProviderStateMixin {
  bool notificationEnabled = true;
  bool tutorialEnabled = true;
  String version = "9.9.9"; // This should come from your app's version
  String lastSyncTime = "Not synced yet";
  String workOfflineStatus = "Not yet started";
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2.withOpacity(.9),
      appBar: CustomAppBar(
        title: 'More',
        onBackPressed: () {},
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader('Preferences'),
              _buildCard([
                _buildSettingItem(
                  title: 'Notifications',
                  icon: Icons.notifications_outlined,
                  trailing: Switch(
                    value: notificationEnabled,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    onChanged: (value) {
                      setState(() {
                        notificationEnabled = value;
                      });
                    },
                    activeColor: AppColors.primaryBlue,
                  ),
                ),
                const Divider(height: 1, color: AppColors.lightGrey2),
                _buildSettingItem(
                  title: 'Tutorial',
                  icon: Icons.school_outlined,
                  trailing: Switch(
                    value: tutorialEnabled,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    onChanged: (value) {
                      setState(() {
                        tutorialEnabled = value;
                      });
                    },
                    activeColor: AppColors.primaryBlue,
                  ),
                ),
              ]),
              const SizedBox(height: 24),
              _buildSectionHeader('Account'),
              _buildCard([
                _buildSettingItem(
                  title: 'Profile',
                  icon: Icons.person_outline,
                  onTap: () {
                    // Navigate to profile
                  },
                ),
                const Divider(height: 1, color: AppColors.lightGrey2),
                _buildSettingItem(
                  title: 'Reset Data',
                  icon: Icons.restore_outlined,
                  onTap: () {
                    // Handle reset data
                  },
                ),
              ]),
              const SizedBox(height: 24),
              _buildSectionHeader('Content'),
              _buildCard([
                _buildSettingItem(
                  title: 'Form Preview',
                  icon: Icons.preview_outlined,
                  onTap: () {
                    // Navigate to form preview
                  },
                ),
                const Divider(height: 1, color: AppColors.lightGrey2),
                _buildSettingItem(
                  title: 'View Terms & Conditions',
                  icon: Icons.description_outlined,
                  onTap: () {
                    // Show T&C
                  },
                ),
                const Divider(height: 1, color: AppColors.lightGrey2),
                _buildSettingItem(
                  title: 'View Privacy Policy',
                  icon: Icons.privacy_tip_outlined,
                  onTap: () {
                    // Show privacy policy
                  },
                ),
                const Divider(height: 1, color: AppColors.lightGrey2),
                _buildSettingItem(
                  title: 'About Store Track',
                  icon: Icons.info_outline,
                  onTap: () {
                    // Show about page
                  },
                ),
                const Divider(height: 1, color: AppColors.lightGrey2),
                _buildSettingItem(
                  title: 'Useful Links',
                  icon: Icons.link_outlined,
                  onTap: () {
                    // Show useful links
                  },
                ),
              ]),
              const SizedBox(height: 24),
              _buildSectionHeader('App Information'),
              _buildCard([
                _buildSettingItem(
                  title: 'Version',
                  icon: Icons.system_update_outlined,
                  trailing: Row(
                    children: [
                      Text(
                        version,
                        style: textTheme.montserratParagraphSmall.copyWith(
                          color: AppColors.blackTint1,
                        ),
                      ),
                      const SizedBox(width: 10),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Update available',
                          style: textTheme.montserratParagraphXsmall.copyWith(
                            color: AppColors.primaryBlue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ]),
              const SizedBox(height: 24),
              _buildSectionHeader('Data Management'),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    _buildActionButton(
                      title: 'Sync Data',
                      icon: Icons.sync_outlined,
                      onPressed: () {
                        // Handle sync data
                      },
                      backgroundColor: const Color(0xFF4CAF50),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.access_time,
                          size: 14,
                          color: AppColors.blackTint1,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          lastSyncTime,
                          style: textTheme.montserratParagraphSmall.copyWith(
                            color: AppColors.blackTint1,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    _buildActionButton(
                      title: 'Work Offline',
                      icon: Icons.cloud_off_outlined,
                      onPressed: () {
                        // Handle work offline
                      },
                      backgroundColor: AppColors.primaryBlue,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.info_outline,
                          size: 14,
                          color: AppColors.blackTint1,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          workOfflineStatus,
                          style: textTheme.montserratParagraphSmall.copyWith(
                            color: AppColors.blackTint1,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.montserratSemiBold.copyWith(
              fontSize: 14,
              color: AppColors.blackTint1,
            ),
      ),
    );
  }

  Widget _buildCard(List<Widget> children) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.defaultBorderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSettingItem({
    required String title,
    required IconData icon,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          child: Row(
            children: [
              Icon(
                icon,
                size: 22,
                color: AppColors.primaryBlue,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.montserratTitleSmall,
              ),
              const Spacer(),
              trailing ??
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppColors.blackTint1,
                  ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required VoidCallback onPressed,
    required Color backgroundColor,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: backgroundColor.withOpacity(0.4),
          shape: RoundedRectangleBorder(
            borderRadius:
                BorderRadius.circular(AppConstants.defaultBorderRadius),
          ),
          padding: const EdgeInsets.symmetric(vertical: 14),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 20),
            const SizedBox(width: 8),
            Text(
              title,
              style:
                  Theme.of(context).textTheme.montserratParagraphSmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
            ),
          ],
        ),
      ),
    );
  }
}
