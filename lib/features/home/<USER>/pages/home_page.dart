import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

import '../../../../config/routes/app_router.gr.dart';
import '../../../../config/themes/app_colors.dart';
import '../../../../core/constants/app_assets.dart';
import '../../../../core/utils/logger.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // Check if the current route is the DashboardRoute
  bool _isDashboardRoute() {
    // First check if we're on the first tab (Dashboard tab)
    return context.router.currentPath == '/home-route';
  }

  @override
  Widget build(BuildContext context) {
    return AutoTabsRouter(
      routes: const [
        DashboardHolderRoute(),
        <PERSON><PERSON><PERSON><PERSON>(),
        <PERSON><PERSON><PERSON><PERSON>(),
        <PERSON>R<PERSON><PERSON>()
      ],
      builder: (context, child) {
        logger('Current route: ${context.router.currentPath}');
        logger(
            'Current route name: ${(ModalRoute.of(context)?.settings.name)}');
        final textTheme = Theme.of(context).textTheme;
        final tabsRouter = AutoTabsRouter.of(context);
        return Scaffold(
          body: child,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            toolbarHeight: 0,
            forceMaterialTransparency: true,
            systemOverlayStyle: SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness:
                  _isDashboardRoute() ? Brightness.light : Brightness.dark,
              systemNavigationBarColor: Colors.transparent,
              systemNavigationBarIconBrightness: Brightness.light,
              statusBarBrightness:
                  _isDashboardRoute() ? Brightness.dark : Brightness.light,
            ),
          ),
          extendBodyBehindAppBar: true,
          bottomNavigationBar: BottomNavigationBar(
              type: BottomNavigationBarType.fixed,
              backgroundColor: Colors.white,
              currentIndex: tabsRouter.activeIndex,
              onTap: tabsRouter.setActiveIndex,
              selectedFontSize: 12,
              unselectedItemColor: AppColors.black.withOpacity(.4),
              selectedItemColor: AppColors.primaryBlue,
              selectedLabelStyle: textTheme.montserratBottomNavigation,
              unselectedLabelStyle: textTheme.montserratBottomNavigation
                  .copyWith(color: AppColors.black.withOpacity(.6)),
              items: [
                BottomNavigationBarItem(
                  icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: tabsRouter.activeIndex == 0
                        ? Image.asset(
                            color: AppColors.primaryBlue,
                            AppAssets.homeDashboard,
                            width: 18,
                          )
                        : Image.asset(
                            color: AppColors.black.withOpacity(.6),
                            AppAssets.homeDashboard,
                            width: 18,
                          ),
                  ),
                  label: 'Dashboard',
                ),
                BottomNavigationBarItem(
                  icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: tabsRouter.activeIndex == 1
                        ? Image.asset(
                            color: AppColors.primaryBlue,
                            AppAssets.homeAssistant,
                            width: 18,
                          )
                        : Image.asset(
                            color: AppColors.black.withOpacity(.6),
                            AppAssets.homeAssistant,
                            width: 18,
                          ),
                  ),
                  label: 'Assistant',
                ),
                BottomNavigationBarItem(
                  icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: tabsRouter.activeIndex == 2
                        ? Image.asset(
                            color: AppColors.primaryBlue,
                            AppAssets.homeProfile,
                            width: 18,
                          )
                        : Image.asset(
                            color: AppColors.black.withOpacity(.6),
                            AppAssets.homeProfile,
                            width: 18,
                          ),
                  ),
                  label: 'Profile',
                ),
                BottomNavigationBarItem(
                  icon: Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: tabsRouter.activeIndex == 3
                        ? Image.asset(
                            color: AppColors.primaryBlue,
                            AppAssets.homeMore,
                            width: 18,
                          )
                        : Image.asset(
                            color: AppColors.black.withOpacity(.6),
                            AppAssets.homeMore,
                            width: 18,
                          ),
                  ),
                  label: 'More',
                ),
              ]
              // BottomNavigationBarItem(
              //   icon: Icon(Icons.more_horiz),
              //   label: 'More',
              // ),
              // ],
              ),
        );
      },
    );
  }
}
