import 'dart:convert';

class SubmitReportResponseEntity {
  Data? data;

  SubmitReportResponseEntity({
    this.data,
  });

  factory SubmitReportResponseEntity.fromRawJson(String str) =>
      SubmitReportResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory SubmitReportResponseEntity.fromJson(Map<String, dynamic> json) =>
      SubmitReportResponseEntity(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  int? answersDeleted;
  int? answersUpdated;
  int? answersInserted;
  int? unacceptedReasonId;

  Data({
    this.answersDeleted,
    this.answersUpdated,
    this.answersInserted,
    this.unacceptedReasonId,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        answersDeleted: json["answers_deleted"],
        answersUpdated: json["answers_updated"],
        answersInserted: json["answers_inserted"],
        unacceptedReasonId: json["unaccepted_reason_id"],
      );

  Map<String, dynamic> toJson() => {
        "answers_deleted": answersDeleted,
        "answers_updated": answersUpdated,
        "answers_inserted": answersInserted,
        "unaccepted_reason_id": unacceptedReasonId,
      };
}
