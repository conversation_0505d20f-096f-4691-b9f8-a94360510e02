import 'dart:convert';

class CalendarResponseEntity {
  Data? data;

  CalendarResponseEntity({
    this.data,
  });

  factory CalendarResponseEntity.fromRawJson(String str) =>
      CalendarResponseEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CalendarResponseEntity.fromJson(Map<String, dynamic> json) =>
      CalendarResponseEntity(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class Data {
  List<CalendarInfo>? calendarInfo;

  Data({
    this.calendarInfo,
  });

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        calendarInfo: json["calendar_info"] == null
            ? []
            : List<CalendarInfo>.from(
                json["calendar_info"]!.map((x) => CalendarInfo.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "calendar_info": calendarInfo == null
            ? []
            : List<dynamic>.from(calendarInfo!.map((x) => x.toJson())),
      };
}

class CalendarInfo {
  DateTime? timestamp;
  bool? dollarSymbol;
  bool? publicHoliday;
  num? budgetAmount;

  CalendarInfo({
    this.timestamp,
    this.dollarSymbol,
    this.publicHoliday,
    this.budgetAmount,
  });

  factory CalendarInfo.fromRawJson(String str) =>
      CalendarInfo.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory CalendarInfo.fromJson(Map<String, dynamic> json) => CalendarInfo(
        timestamp: json["timestamp"] == null
            ? null
            : DateTime.parse(json["timestamp"]),
        dollarSymbol: json["dollar_symbol"] ==
            true, // Ensure it's a boolean and defaults to false if null
        publicHoliday: json["public_holiday"] ==
            true, // Ensure it's a boolean and defaults to false if null
        budgetAmount: json["budget_amount"] ?? 0, // Default to 0 if null
      );

  Map<String, dynamic> toJson() => {
        "timestamp": timestamp?.toIso8601String(),
        "dollar_symbol": dollarSymbol,
        "public_holiday": publicHoliday,
        "budget_amount": budgetAmount,
      };
}
