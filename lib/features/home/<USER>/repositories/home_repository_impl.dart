import 'package:storetrack_app/core/network/network_info.dart';

import '../../../../shared/models/result.dart';
import '../../data/datasources/home_local_datasource.dart';
import '../../data/datasources/home_remote_datasource.dart';
import '../entities/tasks_request_entity.dart';
import '../entities/tasks_response_entity.dart';
import '../../data/repositories/home_repository.dart';
import '../entities/calendar_response_entity.dart';
import '../usecases/get_calendar_usecase.dart';
import '../entities/submit_report_request_entity.dart';
import '../entities/submit_report_response_entity.dart';

class HomeRepositoryImpl implements HomeRepository {
  final HomeRemoteDataSource remoteDataSource;
  final NetworkInfo networkInfo;
  final HomeLocalDataSource localDataSource;

  HomeRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
    required this.localDataSource,
  });

  @override
  Future<Result<TasksResponseEntity>> getTasks(
      TasksRequestEntity request) async {
    if (await networkInfo.isConnected) {
      // Online: fetch from remote and cache locally
      var result = await remoteDataSource.getTasks(request);
      if (result.isSuccess && result.data != null) {
        await localDataSource.saveTasks(result.data!);
      }
      return result;
    } else {
      // Offline: try to get from local cache
      final localData = await localDataSource.getTasks();
      if (localData != null) {
        return Result.success(localData);
      } else {
        return Result.failure(
            'No internet connection and no cached data available.');
      }
    }
  }

  @override
  Future<Result<CalendarResponseEntity>> getCalendarData(
      GetCalendarParams request) async {
    if (await networkInfo.isConnected) {
      // Online: fetch from remote and cache locally
      var result = await remoteDataSource.getCalendarData(request);
      if (result.isSuccess && result.data != null) {
        await localDataSource.saveCalendarInfo(result.data!);
      }
      return result;
    } else {
      // Offline: try to get from local cache
      final localData = await localDataSource.getCalendarInfo();
      if (localData != null) {
        return Result.success(localData);
      } else {
        return Result.failure(
            'No internet connection and no cached calendar data available.');
      }
    }
  }

  @override
  Future<Result<SubmitReportResponseEntity>> submitReport(
      SubmitReportRequestEntity request) async {
    return remoteDataSource.submitReport(request);
  }
}
