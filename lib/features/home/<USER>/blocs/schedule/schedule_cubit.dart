import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_tasks_usecase.dart';
import '../../../domain/entities/submit_report_response_entity.dart';
import '../../../domain/usecases/submit_report_usecase.dart';
import 'schedule_state.dart';

class ScheduleTaskCubit extends Cubit<ScheduleTaskState> {
  final GetTasksUseCase _unscheduleTaskUseCase;
  final GetCalendarUseCase _getCalendarUseCase;
  final SubmitReportUseCase _submitReportUseCase;

  CalendarResponseEntity? _calendarResponse;

  ScheduleTaskCubit(
    this._unscheduleTaskUseCase,
    this._getCalendarUseCase,
    this._submitReportUseCase,
  ) : super(ScheduleTaskInitial());

  // Getter for calendar response
  CalendarResponseEntity? get calendarResponse => _calendarResponse;

  // Combined method to fetch both calendar and task data
  Future<void> getData(TasksRequestEntity request) async {
    emit(ScheduleTaskLoading()); // Signal start of operation

    try {
      // Get both calendar and task data in parallel
      final calendarFuture = _getCalendarUseCase(GetCalendarParams(
        token: request.token,
        userId: request.userId,
      ));

      final tasksFuture = _unscheduleTaskUseCase.call(request);

      // Wait for both futures to complete
      final results = await Future.wait([calendarFuture, tasksFuture]);
      final calendarResult = results[0] as Result<CalendarResponseEntity>;
      final tasksResult = results[1] as Result<TasksResponseEntity>;

      // Process calendar data
      if (calendarResult.isSuccess && calendarResult.data != null) {
        logger('Calendar data available: ${calendarResult.data != null}');

        if (calendarResult.data?.data?.calendarInfo != null) {
          logger(
              'Calendar info count: ${calendarResult.data?.data?.calendarInfo?.length}');

          // Log some calendar info entries for debugging
          final calendarInfo = calendarResult.data?.data?.calendarInfo;
          if (calendarInfo != null && calendarInfo.isNotEmpty) {
            for (var i = 0; i < calendarInfo.length && i < 5; i++) {
              var info = calendarInfo[i];
              logger(
                  'Calendar info $i: timestamp=${info.timestamp}, dollarSymbol=${info.dollarSymbol}, budgetAmount=${info.budgetAmount}');
            }
          }
        }

        // Store the calendar response
        _calendarResponse = calendarResult.data;
      } else if (!calendarResult.isSuccess) {
        logger('Calendar API error: ${calendarResult.error}');
      }

      // Process tasks result
      if (tasksResult.isSuccess) {
        final response = tasksResult.data;
        if (response != null) {
          // Include calendar data in the success state if available
          emit(ScheduleTaskSuccess(response,
              calendarResponse: _calendarResponse));
        } else {
          emit(const ScheduleTaskError(
              'Schedule successful but no response data received.'));
        }
      } else {
        // Failure: Extract error message from Result
        final errorMessage = tasksResult.error?.toString() ??
            'Unknown error occurred while fetching tasks.';
        emit(ScheduleTaskError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in ScheduleTaskCubit: $e");
      emit(ScheduleTaskError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  // Keep this method for backward compatibility and specific task scheduling
  Future<void> scheduleTasks(TasksRequestEntity request) async {
    emit(ScheduleTaskLoading()); // Signal start of operation

    try {
      final Result<TasksResponseEntity> result =
          await _unscheduleTaskUseCase.call(request);

      if (result.isSuccess) {
        final response = result.data;
        if (response != null) {
          // Include calendar data in the success state if available
          emit(ScheduleTaskSuccess(response,
              calendarResponse: _calendarResponse));
        } else {
          emit(const ScheduleTaskError(
              'Schedule successful but no response data received.'));
        }
      } else {
        // Failure: Extract error message from Result
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while scheduling tasks.';
        emit(ScheduleTaskError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in ScheduleTaskCubit: $e");
      emit(ScheduleTaskError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  // submit report
  Future<void> submitReport(SubmitReportRequestEntity request) async {
    emit(ScheduleTaskLoading()); // Signal start of operation

    try {
      final Result<SubmitReportResponseEntity> result =
          await _submitReportUseCase.call(request);

      if (result.isSuccess) {
        final response = result.data;
        if (response != null) {
          // Include calendar data in the success state if available
        } else {
          emit(const ScheduleTaskError(
              'Schedule successful but no response data received.'));
        }
      } else {
        // Failure: Extract error message from Result
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while scheduling tasks.';
        emit(ScheduleTaskError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in ScheduleTaskCubit: $e");
      emit(ScheduleTaskError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  void resetState() {
    emit(ScheduleTaskInitial());
  }
}
