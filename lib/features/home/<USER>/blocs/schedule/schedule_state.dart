import 'package:equatable/equatable.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

abstract class ScheduleTaskState extends Equatable {
  const ScheduleTaskState();

  @override
  List<Object?> get props => [];
}

class ScheduleTaskInitial extends ScheduleTaskState {}

class ScheduleTaskLoading extends ScheduleTaskState {}

class ScheduleTaskSuccess extends ScheduleTaskState {
  final TasksResponseEntity response;
  final CalendarResponseEntity? calendarResponse;

  const ScheduleTaskSuccess(this.response, {this.calendarResponse});

  @override
  List<Object?> get props => [response, calendarResponse];
}

class ScheduleTaskError extends ScheduleTaskState {
  final String message;

  const ScheduleTaskError(this.message);

  @override
  List<Object?> get props => [message];
}

class CalendarDataSuccess extends ScheduleTaskState {
  final CalendarResponseEntity calendarResponse;

  const CalendarDataSuccess(this.calendarResponse);

  @override
  List<Object?> get props => [calendarResponse];
}
