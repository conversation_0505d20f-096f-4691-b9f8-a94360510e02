import 'package:equatable/equatable.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

sealed class TodayPageState extends Equatable {
  const TodayPageState();

  @override
  List<Object?> get props => [];
}

final class TodayPageInitial extends TodayPageState {}

final class TodayPageLoading extends TodayPageState {}

final class TodayPageSuccess extends TodayPageState {
  final TasksResponseEntity tasksResponse;
  final CalendarResponseEntity? calendarResponse;

  const TodayPageSuccess({
    required this.tasksResponse,
    this.calendarResponse,
  });

  @override
  List<Object?> get props => [tasksResponse, calendarResponse];
}

final class TodayPageError extends TodayPageState {
  final String message;

  const TodayPageError(this.message);

  @override
  List<Object?> get props => [message];
}
