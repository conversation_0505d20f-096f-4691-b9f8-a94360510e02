import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import '../../../domain/entities/tasks_request_entity.dart';
import '../../../domain/entities/tasks_response_entity.dart';
import '../../../domain/usecases/get_tasks_usecase.dart';
import 'today_page_state.dart';

class TodayPageCubit extends Cubit<TodayPageState> {
  final GetTasksUseCase _getTasksUseCase;
  final GetCalendarUseCase _getCalendarUseCase;

  TodayPageCubit(this._getTasksUseCase, this._getCalendarUseCase)
      : super(TodayPageInitial());

  Future<void> getData(TasksRequestEntity request) async {
    emit(TodayPageLoading());

    try {
      // Get tasks data
      final Result<TasksResponseEntity> tasksResult =
          await _getTasksUseCase(request);

      // Get calendar data
      final Result<CalendarResponseEntity> calendarResult =
          await _getCalendarUseCase(GetCalendarParams(
        token: request.token,
        userId: request.userId,
      ));

      // Debug log for calendar data
      logger('Calendar API result success: ${calendarResult.isSuccess}');
      if (calendarResult.isSuccess && calendarResult.data != null) {
        logger('Calendar data available: ${calendarResult.data != null}');
        if (calendarResult.data?.data != null) {
          logger(
              'Calendar info available: ${calendarResult.data?.data?.calendarInfo != null}');
          if (calendarResult.data?.data?.calendarInfo != null) {
            logger(
                'Calendar info count: ${calendarResult.data?.data?.calendarInfo?.length}');

            // Log some calendar info entries for debugging
            final calendarInfo = calendarResult.data?.data?.calendarInfo;
            if (calendarInfo != null && calendarInfo.isNotEmpty) {
              for (var i = 0; i < calendarInfo.length && i < 5; i++) {
                var info = calendarInfo[i];
                logger(
                    'Calendar info $i: timestamp=${info.timestamp}, dollarSymbol=${info.dollarSymbol}, budgetAmount=${info.budgetAmount}');
              }
            }
          }
        }
      } else if (!calendarResult.isSuccess) {
        logger('Calendar API error: ${calendarResult.error}');
      }

      if (tasksResult.isSuccess) {
        if (calendarResult.isSuccess) {
          // Both requests succeeded
          logger('Emitting TodayPageSuccess with calendar data');
          emit(TodayPageSuccess(
            tasksResponse: tasksResult.data!,
            calendarResponse: calendarResult.data,
          ));
        } else {
          // Only tasks request succeeded
          logger('Emitting TodayPageSuccess without calendar data');
          emit(TodayPageSuccess(
            tasksResponse: tasksResult.data!,
          ));
        }
      } else {
        // Tasks request failed
        final errorMessage = tasksResult.error?.toString() ??
            'Unknown error occurred while fetching tasks.';
        emit(TodayPageError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in TodayPageCubit: $e");
      emit(TodayPageError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  void resetState() {
    emit(TodayPageInitial());
  }
}
