import 'package:equatable/equatable.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

abstract class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

class DashboardInitial extends DashboardState {}

class DashboardLoading extends DashboardState {}

class DashboardLoaded extends DashboardState {
  final TasksResponseEntity response;
  final int countUnscheduled;
  final int countScheduled;
  final int countPos;
  final int countCompleted;
  final int countToday;

  const DashboardLoaded({
    required this.response,
    required this.countUnscheduled,
    required this.countScheduled,
    required this.countPos,
    required this.countCompleted,
    required this.countToday,
  });

  @override
  List<Object?> get props => [
        response,
        countUnscheduled,
        countScheduled,
        countPos,
        countCompleted,
        countToday,
      ];
}

class DashboardError extends DashboardState {
  final String message;

  const DashboardError(this.message);

  @override
  List<Object?> get props => [message];
}
