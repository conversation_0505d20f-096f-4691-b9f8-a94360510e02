import 'package:bloc/bloc.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_tasks_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import 'dashboard_state.dart';

class DashboardCubit extends Cubit<DashboardState> {
  final GetTasksUseCase _unscheduleTaskUseCase;

  DashboardCubit(this._unscheduleTaskUseCase) : super(DashboardInitial());

  Future<void> fetchDashboardData(TasksRequestEntity request) async {
    emit(DashboardLoading());

    try {
      final Result<TasksResponseEntity> result =
          await _unscheduleTaskUseCase(request);

      if (result.isSuccess) {
        final response = result.data;
        if (response != null) {
          // Calculate counts
          final counts = _calculateCounts(response);

          emit(DashboardLoaded(
            response: response,
            countUnscheduled: counts['unscheduled'] ?? 0,
            countScheduled: counts['scheduled'] ?? 0,
            countPos: counts['pos'] ?? 0,
            countCompleted: counts['completed'] ?? 0,
            countToday: counts['today'] ?? 0,
          ));
        } else {
          emit(const DashboardError(
              'Dashboard data fetch successful but no response data received.'));
        }
      } else {
        // Failure: Extract error message from Result
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while fetching dashboard data.';
        emit(DashboardError(errorMessage));
      }
    } catch (e) {
      print("Unexpected error in DashboardCubit: $e");
      emit(DashboardError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  Map<String, int> _calculateCounts(TasksResponseEntity response) {
    // Initialize counts
    int countUnscheduled = 0;
    int countScheduled = 0;
    int countCompleted = 0;
    int countToday = 0;
    int countPos = 0;

    // Get today's date at midnight for comparison
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Extract all tasks from the response
    List<TaskDetail> allTasks = response.data?["add_tasks"] ?? [];

    for (var task in allTasks) {
      // Count unscheduled tasks
      if (task.taskStatus == "Tentative" &&
          task.taskId != 0 &&
          task.isOpen == false) {
        countUnscheduled++;
      }

      // Count scheduled tasks
      if (task.taskStatus == "Confirmed" && task.isOpen == false) {
        countScheduled++;

        // Check if task is scheduled for today
        final scheduledDate = task.scheduledTimeStamp;
        if (scheduledDate != null) {
          final taskDate = DateTime(
              scheduledDate.year, scheduledDate.month, scheduledDate.day);
          if (taskDate.isAtSameMomentAs(today)) {
            countToday++;
          }
        }
      }

      // Count completed tasks
      if ((task.taskStatus == "Successful" ||
          task.taskStatus == "Unsuccessful")) {
        countCompleted++;
      }

      // Count POS tasks
      if (task.posRequired == true) {
        countPos++;
      }
    }

    return {
      'unscheduled': countUnscheduled,
      'scheduled': countScheduled,
      'completed': countCompleted,
      'today': countToday,
      'pos': countPos,
    };
  }

  void resetState() {
    emit(DashboardInitial());
  }
}
