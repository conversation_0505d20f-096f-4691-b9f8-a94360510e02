import 'package:equatable/equatable.dart';

import '../../../domain/entities/tasks_response_entity.dart';
import '../../../domain/entities/calendar_response_entity.dart';

sealed class UnscheduleTaskState extends Equatable {
  const UnscheduleTaskState();

  @override
  List<Object?> get props => [];
}

final class UnscheduleTaskInitial extends UnscheduleTaskState {}

final class UnscheduleTaskLoading extends UnscheduleTaskState {}

final class UnscheduleTaskSuccess extends UnscheduleTaskState {
  final TasksResponseEntity tasksResponse;
  final CalendarResponseEntity calendarResponse;

  const UnscheduleTaskSuccess({
    required this.tasksResponse,
    required this.calendarResponse,
  });

  @override
  List<Object?> get props => [tasksResponse];
}

final class UnscheduleTaskError extends UnscheduleTaskState {
  final String message;

  const UnscheduleTaskError(this.message);

  @override
  List<Object?> get props => [message];
}
