import '../../../../shared/models/result.dart';
import '../../data/datasources/notification_datasource.dart';
import '../../data/models/notification_req.dart';
import '../../data/models/notification_response.dart';
import 'notification_repository.dart';

class NotificationRepositoryImpl implements NotificationRepository {
  NotificationRepositoryImpl(this._remoteDataSource);

  final NotificationRemoteDataSource _remoteDataSource;

  @override
  Future<Result<NotificationResponse>> getAlerts(
      NotificationReqParams request) async {
    return await _remoteDataSource.getAlerts(request);
  }
}
