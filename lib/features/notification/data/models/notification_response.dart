// // To parse this JSON data, do
// //
// //     final notificationResponse = notificationResponseFromJson(jsonString);

// import 'dart:convert';

// NotificationResponse notificationResponseFromJson(String str) =>
//     NotificationResponse.fromJson(json.decode(str));

// String notificationResponseToJson(NotificationResponse data) =>
//     json.encode(data.toJson());

// class NotificationResponse {
//   final Data? data;

//   NotificationResponse({
//     this.data,
//   });

//   NotificationResponse copyWith({
//     Data? data,
//   }) =>
//       NotificationResponse(
//         data: data ?? this.data,
//       );

//   factory NotificationResponse.fromJson(Map<String, dynamic> json) =>
//       NotificationResponse(
//         data: json["data"] == null ? null : Data.fromJson(json["data"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "data": data?.toJson(),
//       };
// }

// class Data {
//   final List<Alert>? alerts;

//   Data({
//     this.alerts,
//   });

//   Data copyWith({
//     List<Alert>? alerts,
//   }) =>
//       Data(
//         alerts: alerts ?? this.alerts,
//       );

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         alerts: json["alerts"] == null
//             ? []
//             : List<Alert>.from(json["alerts"]!.map((x) => Alert.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "alerts": alerts == null
//             ? []
//             : List<dynamic>.from(alerts!.map((x) => x.toJson())),
//       };
// }

// class Alert {
//   final int? alertId;
//   final int? clientId;
//   final int? userId;
//   final String? title;
//   final String? shortDescription;
//   final String? comment;
//   final String? sender;
//   final DateTime? date;
//   final String? clientLogoUrl;
//   final bool? isRead;

//   Alert({
//     this.alertId,
//     this.clientId,
//     this.userId,
//     this.title,
//     this.shortDescription,
//     this.comment,
//     this.sender,
//     this.date,
//     this.clientLogoUrl,
//     this.isRead,
//   });

//   Alert copyWith({
//     int? alertId,
//     int? clientId,
//     int? userId,
//     String? title,
//     String? shortDescription,
//     String? comment,
//     String? sender,
//     DateTime? date,
//     String? clientLogoUrl,
//     bool? isRead,
//   }) =>
//       Alert(
//         alertId: alertId ?? this.alertId,
//         clientId: clientId ?? this.clientId,
//         userId: userId ?? this.userId,
//         title: title ?? this.title,
//         shortDescription: shortDescription ?? this.shortDescription,
//         comment: comment ?? this.comment,
//         sender: sender ?? this.sender,
//         date: date ?? this.date,
//         clientLogoUrl: clientLogoUrl ?? this.clientLogoUrl,
//         isRead: isRead ?? this.isRead,
//       );

//   factory Alert.fromJson(Map<String, dynamic> json) => Alert(
//         alertId: json["alert_id"],
//         clientId: json["client_id"],
//         userId: json["user_id"],
//         title: json["title"],
//         shortDescription: json["short_description"],
//         comment: json["comment"],
//         sender: json["sender"],
//         date: json["date"] == null ? null : DateTime.parse(json["date"]),
//         clientLogoUrl: json["client_logo_url"],
//         isRead: json["is_read"],
//       );

//   Map<String, dynamic> toJson() => {
//         "alert_id": alertId,
//         "client_id": clientId,
//         "user_id": userId,
//         "title": title,
//         "short_description": shortDescription,
//         "comment": comment,
//         "sender": sender,
//         "date": date?.toIso8601String(),
//         "client_logo_url": clientLogoUrl,
//         "is_read": isRead,
//       };
// }
class NotificationResponse {
  final NotificationData data;

  NotificationResponse({required this.data});

  factory NotificationResponse.fromJson(Map<String, dynamic> json) {
    return NotificationResponse(
      data: NotificationData.fromJson(json['data']),
    );
  }
}

class NotificationData {
  final List<Alert> alerts;

  NotificationData({required this.alerts});

  factory NotificationData.fromJson(Map<String, dynamic> json) {
    var alertsList =
        (json['alerts'] as List).map((alert) => Alert.fromJson(alert)).toList();
    return NotificationData(alerts: alertsList);
  }
}

class Alert {
  final num alertId;
  final num clientId;
  final num userId;
  final String title;
  final String shortDescription;
  final String comment;
  final String sender;
  final DateTime date;
  final String clientLogoUrl;
  final bool isRead;
  final String clientName;
  final String storeName;
  final String storeAddress;
  final String storeSuburb;
  final String storePostcode;
  final String taskId;
  final String taskDuration;
  final String cycleName;

  Alert({
    required this.alertId,
    required this.clientId,
    required this.userId,
    required this.title,
    required this.shortDescription,
    required this.comment,
    required this.sender,
    required this.date,
    required this.clientLogoUrl,
    required this.isRead,
    required this.clientName,
    required this.storeName,
    required this.storeAddress,
    required this.storeSuburb,
    required this.storePostcode,
    required this.taskId,
    required this.taskDuration,
    required this.cycleName,
  });

  factory Alert.fromJson(Map<String, dynamic> json) {
    return Alert(
      alertId: json['alert_id'],
      clientId: json['client_id'],
      userId: json['user_id'],
      title: json['title'],
      shortDescription: json['short_description'],
      comment: json['comment'],
      sender: json['sender'],
      date: DateTime.parse(json['date']),
      clientLogoUrl: json['client_logo_url'],
      isRead: json['is_read'],
      clientName: json['client_name'],
      storeName: json['store_name'],
      storeAddress: json['store_address'],
      storeSuburb: json['store_suburb'],
      storePostcode: json['store_postcode'],
      taskId: json['task_id'],
      taskDuration: json['task_duration'],
      cycleName: json['cycle_name'],
    );
  }
}
