import 'package:dio/dio.dart';

import '../../../../shared/models/result.dart';
import '../models/notification_req.dart';
import '../models/notification_response.dart';

abstract class NotificationRemoteDataSource {
  /// Get Alerts
  Future<Result<NotificationResponse>> getAlerts(NotificationReqParams request);
}

class NotificationRemoteDataSourceImpl implements NotificationRemoteDataSource {
  /// Constructor
  NotificationRemoteDataSourceImpl(this._dio);

  final Dio _dio;

  @override
  Future<Result<NotificationResponse>> getAlerts(
      NotificationReqParams request) async {
    try {
      // Assuming NotificationReqParams has a toJson() method for query parameters
      final response = await _dio.get(
        '/alert?user_id=${request.id}&token=${request.token}',
        // Use queryParameters for GET requests
      );
      if (response.statusCode == 200) {
        final data1 = {
          "data": {
            "alerts": [
              {
                "alert_id": 114464,
                "client_id": 700,
                "user_id": 0,
                "title":
                    "HIGHWAY SUPERMART - KUMEU for cycle IT Standard Cycle",
                "short_description": "COLLEGE SU",
                "comment":
                    "COLLEGE SUPERETTE - ALEXANDRA ST for cycle IT Standard Cycle: test alert 2",
                "sender": "Ami Hatzvi",
                "date": "2024-05-31T00:00:00",
                "client_logo_url": "",
                "is_read": true
              },
              {
                "alert_id": 114507,
                "client_id": 700,
                "user_id": 0,
                "title": "Z STRATFORD for cycle IT Standard Cycle",
                "short_description": "COLLEGE SU",
                "comment":
                    "COLLEGE SUPERETTE - ALEXANDRA ST for cycle IT Standard Cycle: test alert 2",
                "sender": "Ami Hatzvi",
                "date": "2024-05-31T00:00:00",
                "client_logo_url": "",
                "is_read": true
              },
              {
                "alert_id": 192674,
                "client_id": 700,
                "user_id": 0,
                "title":
                    ".LIFE CENTRAL CHANG WATTANA for cycle IT Standard Cycle",
                "short_description": "new alert ",
                "comment": "new alert test ",
                "sender": "Khem Shrestha",
                "date": "2025-03-06T00:00:00",
                "client_logo_url": "",
                "is_read": true
              },
              {
                "alert_id": 192674,
                "client_id": 700,
                "user_id": 0,
                "title":
                    ".LIFE CENTRAL CHANG WATTANA for cycle IT Standard Cycle",
                "short_description": "new alert ",
                "comment": "new alert test ",
                "sender": "Khem Shrestha",
                "date": "2025-03-06T00:00:00",
                "client_logo_url": "",
                "is_read": true
              },
              {
                "alert_id": 192674,
                "client_id": 700,
                "user_id": 0,
                "title":
                    ".LIFE CENTRAL CHANG WATTANA for cycle IT Standard Cycle",
                "short_description": "new alert ",
                "comment": "new alert test ",
                "sender": "Khem Shrestha",
                "date": "2025-03-06T00:00:00",
                "client_logo_url": "",
                "is_read": true
              },
              {
                "alert_id": 192674,
                "client_id": 700,
                "user_id": 0,
                "title":
                    ".LIFE CENTRAL CHANG WATTANA for cycle IT Standard Cycle",
                "short_description": "new alert ",
                "comment": "new alert test ",
                "sender": "Khem Shrestha",
                "date": "2025-03-06T00:00:00",
                "client_logo_url": "",
                "is_read": true
              }
            ]
          }
        };
        var data = response.data;
        final notificationResponse = NotificationResponse.fromJson(data);
        return Result.success(notificationResponse);
      } else {
        return Result.failure(
            '${response.statusCode} - ${response.data?['message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      return Result.failure(
          'An error occurred during calling getAlerts: ${e.toString()}');
    }
  }
}
