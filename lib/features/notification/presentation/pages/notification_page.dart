import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/date_time_utils.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/notification/data/models/notification_req.dart';
import 'package:storetrack_app/features/notification/data/models/notification_response.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import '../../../../config/themes/app_colors.dart';
import '../../../../core/storage/data_manager.dart';
import '../../../../di/service_locator.dart';
import '../blocs/notification_cubit.dart';
import '../blocs/notification_state.dart';
import '../widgets/notification_card.dart';

@RoutePage()
class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  NotificationResponse? notification;

  // Map to store alerts grouped by date
  Map<String, List<dynamic>> groupedAlerts = {};

  @override
  void initState() {
    super.initState();
    _fetchNotifications();
  }

  Future<void> _fetchNotifications() async {
    try {
      final dataManager = sl<DataManager>();
      final userId = await dataManager.getUserId() ?? '0';
      final token = await dataManager.getAuthToken() ?? '0';

      if (mounted) {
        context
            .read<NotificationCubit>()
            .fetchAlerts(NotificationReqParams(id: userId, token: token));
      }
    } catch (e) {
      logger('Error fetching notifications: $e');
    }
  }

  // Function to group alerts by date
  void _groupAlertsByDate() {
    groupedAlerts.clear();
    if (notification?.data.alerts != null) {
      for (var alert in notification!.data.alerts) {
        // Format the date string for display and grouping
        final dateObj = (alert.date);
        final formattedDate = DateFormat('EEE dd MMM').format(dateObj);

        if (!groupedAlerts.containsKey(formattedDate)) {
          groupedAlerts[formattedDate] = [];
        }

        groupedAlerts[formattedDate]!.add(alert);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return BlocConsumer<NotificationCubit, NotificationState>(
        listener: (context, state) {
      if (state is NotificationError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(state.message),
            backgroundColor: Colors.red,
          ),
        );
      }
    }, builder: (context, state) {
      if (state is NotificationLoaded) {
        notification = state.notificationResponse;
        _groupAlertsByDate(); // Group alerts when data is loaded
      }

      return Scaffold(
        backgroundColor: Colors.white,
        appBar: const CustomAppBar(
          title: 'Notifications',
          backgroundColor: Colors.white,
          elevation: 0,
        ),
        body: state is NotificationLoading
            ? const Center(child: CircularProgressIndicator())
            : state is NotificationError
                ? const Center(child: Text('Error loading notifications'))
                : groupedAlerts.isEmpty
                    ? const Center(child: Text('No notifications available'))
                    : ListView.builder(
                        itemCount: groupedAlerts.length,
                        itemBuilder: (context, index) {
                          // Get date and alerts for this group
                          final date = groupedAlerts.keys.elementAt(index);
                          final alertsForThisDate = groupedAlerts[date]!;

                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(
                                  left: 16,
                                  top: 20,
                                  bottom: 10,
                                ),
                                child: Text(
                                  date,
                                  style:
                                      textTheme.montserratTitleSmall.copyWith(
                                    color: index == 0
                                        ? AppColors.primaryBlue
                                        : AppColors.black,
                                  ),
                                ),
                              ),
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: alertsForThisDate.length,
                                itemBuilder: (context, alertIndex) {
                                  final alert = alertsForThisDate[alertIndex];

                                  var date = alert.date;

                                  // Calculate time ago from the actual date
                                  final timeAgo = date != null
                                      ? getTimeAgo(date)
                                      : 'Unknown time';

                                  // Determine notification type based on index (for example purposes)
                                  // In a real app, you would determine this based on data from the API
                                  NotificationType notificationType;
                                  // if (alertIndex == 0) {
                                  //   notificationType = NotificationType.urgent;
                                  // } else if (alertIndex % 2 == 0) {
                                  //   notificationType = NotificationType.money;
                                  // } else {
                                  notificationType = NotificationType.message;
                                  // }

                                  return NotificationCard(
                                    type: notificationType,
                                    message: alert.comment ?? 'No message',
                                    company:
                                        alert.shortDescription ?? 'Unknown',
                                    task: alert.title ?? 'No task',
                                    location: alert.storeAddress ??
                                        'No location', // Add location field to your alert model if available
                                    timeAgo: timeAgo,
                                    duration: '${alert.taskDuration}m' ??
                                        '0m', // Add duration field to your alert model if available
                                  );
                                },
                              ),
                              if (index < groupedAlerts.length - 1)
                                Padding(
                                  padding: const EdgeInsets.only(top: 8.0),
                                  child: Divider(
                                    height: 1,
                                    thickness: 1,
                                    color: AppColors.appBarBorderBlack,
                                  ),
                                ),
                            ],
                          );
                        },
                      ),
      );
    });
  }
}
