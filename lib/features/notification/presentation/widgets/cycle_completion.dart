import 'package:flutter/material.dart';

class CycleCompletionWidget extends StatelessWidget {
  final double percentage;
  final String title;
  final int segments;
  final Color activeColor;
  final Color inactiveColor;
  final Color borderColor;
  final Color backgroundColor;
  final Color textColor;
  final double height;
  final double borderRadius;

  const CycleCompletionWidget({
    super.key,
    required this.percentage,
    this.title = 'Cycle Completion',
    this.segments = 10,
    this.activeColor = const Color(0xFF3B9AE1),
    this.inactiveColor = const Color(0xFFE5E5E5),
    this.borderColor = Colors.white,
    this.backgroundColor = Colors.white,
    this.textColor = const Color(0xFF1A2B40),
    this.height = 200.0,
    this.borderRadius = 20.0,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate how many segments should be filled
    final activeSections = (percentage / 100 * segments).floor();

    return Container(
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Title
          Text(
            title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),

          // Percentage text
          Text(
            '${percentage.toInt()}%',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          const SizedBox(height: 12),

          // Segmented progress bar
          Row(
            children: [
              Expanded(
                child: Stack(
                  alignment: Alignment.centerLeft,
                  children: [
                    // Progress bar background
                    Container(
                      height: 12,
                      decoration: BoxDecoration(
                        color: inactiveColor,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),

                    // Segmented progress indicators
                    Row(
                      children: List.generate(segments, (index) {
                        // Add small gap between segments
                        final isLast = index == segments - 1;
                        return Expanded(
                          child: Container(
                            margin: EdgeInsets.only(right: isLast ? 0 : 2),
                            height: 12,
                            decoration: BoxDecoration(
                              color: index < activeSections
                                  ? activeColor
                                  : inactiveColor,
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                        );
                      }),
                    ),

                    // Vertical indicator line showing exact percentage
                    Positioned(
                      left: percentage /
                          100 *
                          MediaQuery.of(context).size.width *
                          0.8, // Adjustment factor
                      child: Container(
                        width: 2,
                        height: 20,
                        color: textColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
