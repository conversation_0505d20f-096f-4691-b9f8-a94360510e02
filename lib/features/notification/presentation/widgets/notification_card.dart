import 'package:flutter/material.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

import '../../../../config/themes/app_colors.dart';
import '../../../../core/constants/app_assets.dart';

enum NotificationType {
  urgent,
  money,
  message,
}

class NotificationCard extends StatelessWidget {
  final NotificationType type;
  final String message;
  final String company;
  final String task;
  final String location;
  final String timeAgo;
  final String duration;

  const NotificationCard({
    super.key,
    required this.type,
    required this.message,
    required this.company,
    required this.task,
    required this.location,
    required this.timeAgo,
    required this.duration,
  });

  // Get icon asset based on notification type
  String _getIconAsset() {
    switch (type) {
      case NotificationType.urgent:
        return AppAssets.alertLightening;
      case NotificationType.money:
        return AppAssets.notificationDollar;
      case NotificationType.message:
        return AppAssets.alertMessage;
    }
  }

  // Get icon color based on notification type
  Color _getIconColor() {
    switch (type) {
      case NotificationType.urgent:
        return AppColors.richOrange;
      case NotificationType.money:
        return AppColors.darkYellow15;
      case NotificationType.message:
        return AppColors.black;
    }
  }

  // Get background color based on notification type
  Color _getBackgroundColor() {
    switch (type) {
      case NotificationType.urgent:
        return AppColors.richOrange15;
      case NotificationType.money:
        return AppColors.richYellow15;
      case NotificationType.message:
        return AppColors.lightGrey2;
    }
  }

  // Get text color based on notification type
  Color _getTextColor() {
    switch (type) {
      case NotificationType.urgent:
        return AppColors.darkOrange50;
      case NotificationType.money:
        return AppColors.darkYellow50;
      case NotificationType.message:
        return AppColors.black;
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _getBackgroundColor(),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Center(
                        child: Image.asset(
                          _getIconAsset(),
                          color: _getIconColor(),
                          width: 20,
                          height: 20,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                      color: _getBackgroundColor(),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            message,
                            style: textTheme.montserratParagraphSmall.copyWith(
                              color: _getTextColor(),
                            ),
                          ),
                          const SizedBox(
                            height: 12,
                          ),
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(14),
                              border: Border.all(
                                color: AppColors.black20,
                                width: 1,
                              ),
                            ),
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      company,
                                      style: textTheme.montserratTitleExtraSmall
                                          .copyWith(
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    const Spacer(),
                                    const Icon(
                                      Icons.access_time,
                                      size: 18,
                                      color: AppColors.blackTint1,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      duration,
                                      style: textTheme.montserratTitleExtraSmall
                                          .copyWith(
                                        color: AppColors.blackTint1,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  task,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: textTheme.montserratTableSmall,
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    Image.asset(
                                      AppAssets.notificationLocation,
                                      scale: 5,
                                      color: Colors.black,
                                    ),
                                    const SizedBox(width: 4),
                                    Expanded(
                                      child: Container(
                                        child: Text(
                                          maxLines: 1,
                                          overflow: TextOverflow.clip,
                                          location,
                                          style: textTheme.montserratTableSmall
                                              .copyWith(
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          // overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            height: 12,
                          ),
                          Align(
                            alignment: Alignment.centerLeft,
                            child: Text(timeAgo,
                                style: textTheme.montserratRegular.copyWith(
                                  fontSize: 12,
                                  color: AppColors.blackTint1,
                                )),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
