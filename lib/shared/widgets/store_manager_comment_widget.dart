import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'comment_widget.dart';

/// A widget for displaying store manager comments with a chat icon
class StoreManagerCommentWidget extends StatelessWidget {
  /// The comment text content
  final String text;

  /// Custom background color (optional)
  final Color? backgroundColor;

  /// Custom text color (optional)
  final Color? textColor;

  const StoreManagerCommentWidget({
    super.key,
    required this.text,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return CommentWidget(
      icon: Image.asset(
        AppAssets.alertMessage,
        scale: 4,
        color: AppColors.black,
      ),
      text: text,
      backgroundColor: backgroundColor ?? Colors.grey.shade200,
      textColor: textColor ?? AppColors.black,
    );
  }
}
