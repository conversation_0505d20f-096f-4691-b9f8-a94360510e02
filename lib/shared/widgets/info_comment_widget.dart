import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'comment_widget.dart';

/// A widget for displaying general information comments
class InfoCommentWidget extends StatelessWidget {
  /// The comment text content
  final String text;

  /// Custom background color (optional)
  final Color? backgroundColor;

  /// Custom text color (optional)
  final Color? textColor;

  const InfoCommentWidget({
    super.key,
    required this.text,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return CommentWidget(
      icon: SvgPicture.asset(
        'assets/icons/message.svg',
        width: 24,
        height: 24,
        colorFilter: const ColorFilter.mode(
          AppColors.black,
          BlendMode.srcIn,
        ),
      ),
      text: text,
      backgroundColor: backgroundColor ?? AppColors.lightGrey2,
      textColor: textColor ?? AppColors.black,
    );
  }
}
