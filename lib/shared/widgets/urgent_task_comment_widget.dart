import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'comment_widget.dart';

/// A widget for displaying urgent task comments with a lightning icon
class UrgentTaskCommentWidget extends StatelessWidget {
  /// The comment text content
  final String text;

  /// Custom background color (optional)
  final Color? backgroundColor;

  /// Custom text color (optional)
  final Color? textColor;

  const UrgentTaskCommentWidget({
    super.key,
    required this.text,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return CommentWidget(
      icon: Image.asset(
        AppAssets.alertLightening,
        scale: 4,
        color: AppColors.richOrange,
      ),
      text: text,
      backgroundColor: backgroundColor ?? Colors.orange.shade50,
      textColor: textColor ?? Colors.orange.shade900,
    );
  }
}
