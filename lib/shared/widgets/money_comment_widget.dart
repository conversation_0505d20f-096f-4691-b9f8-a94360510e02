import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'comment_widget.dart';

/// A widget for displaying opportunity-related comments
class MoneyCommentWidget extends StatelessWidget {
  /// The comment text content
  final String text;

  /// Custom background color (optional)
  final Color? backgroundColor;

  /// Custom text color (optional)
  final Color? textColor;

  const MoneyCommentWidget({
    super.key,
    required this.text,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return CommentWidget(
      icon: SvgPicture.asset(
        'assets/icons/cash.svg',
        width: 24,
        height: 24,
        colorFilter:
            const ColorFilter.mode(AppColors.darkYellow15, BlendMode.srcIn),
      ),
      text: text,
      backgroundColor:
          backgroundColor ?? AppColors.richYellow15, // rgba(255, 193, 14, 0.15)
      textColor: textColor ?? AppColors.darkYellow50,
    );
  }
}
