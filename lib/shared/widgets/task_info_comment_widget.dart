import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'comment_widget.dart';

/// A widget for displaying task information comments with a document icon
class TaskInfoCommentWidget extends StatelessWidget {
  /// The comment text content
  final String text;

  /// Optional title for the task info
  final String? title;
  final bool? isInfo;

  /// Custom background color (optional)
  final Color? backgroundColor;

  /// Custom text color (optional)
  final Color? textColor;

  const TaskInfoCommentWidget({
    super.key,
    required this.text,
    this.title,
    this.backgroundColor,
    this.textColor,
    this.isInfo,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return CommentWidget(
      isInfo: isInfo,
      icon: const Icon(
        Icons.description_outlined,
        size: 20,
        color: AppColors.black,
      ),
      text: title != null ? '$title\n\n$text' : text,
      backgroundColor: backgroundColor ?? Colors.white,
      textColor: textColor ?? AppColors.black,
      customTextBuilder: title != null
          ? (context, text) {
              final parts = text.split('\n\n');
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    parts[0],
                    style: textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: textColor ?? AppColors.black,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    parts[1],
                    style: textTheme.bodySmall?.copyWith(
                      color: textColor ?? AppColors.black,
                    ),
                  ),
                ],
              );
            }
          : null,
    );
  }
}
